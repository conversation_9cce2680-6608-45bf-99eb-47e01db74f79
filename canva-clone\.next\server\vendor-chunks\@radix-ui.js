"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcHJpbWl0aXZlL2Rpc3QvaW5kZXgubWpzP2YyYmYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvY29yZS9wcmltaXRpdmUvc3JjL3ByaW1pdGl2ZS50c3hcbmZ1bmN0aW9uIGNvbXBvc2VFdmVudEhhbmRsZXJzKG9yaWdpbmFsRXZlbnRIYW5kbGVyLCBvdXJFdmVudEhhbmRsZXIsIHsgY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID0gdHJ1ZSB9ID0ge30pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIGhhbmRsZUV2ZW50KGV2ZW50KSB7XG4gICAgb3JpZ2luYWxFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgaWYgKGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9PT0gZmFsc2UgfHwgIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHtcbiAgICAgIHJldHVybiBvdXJFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgfVxuICB9O1xufVxuZXhwb3J0IHtcbiAgY29tcG9zZUV2ZW50SGFuZGxlcnNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/composeRefs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => refs.forEach((ref) => setRef(ref, node));\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLDhDQUFpQjtBQUMxQjtBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1jb21wb3NlLXJlZnMvZGlzdC9pbmRleC5tanM/ZDI5OSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9jb21wb3NlLXJlZnMvc3JjL2NvbXBvc2VSZWZzLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiBzZXRSZWYocmVmLCB2YWx1ZSkge1xuICBpZiAodHlwZW9mIHJlZiA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgcmVmKHZhbHVlKTtcbiAgfSBlbHNlIGlmIChyZWYgIT09IG51bGwgJiYgcmVmICE9PSB2b2lkIDApIHtcbiAgICByZWYuY3VycmVudCA9IHZhbHVlO1xuICB9XG59XG5mdW5jdGlvbiBjb21wb3NlUmVmcyguLi5yZWZzKSB7XG4gIHJldHVybiAobm9kZSkgPT4gcmVmcy5mb3JFYWNoKChyZWYpID0+IHNldFJlZihyZWYsIG5vZGUpKTtcbn1cbmZ1bmN0aW9uIHVzZUNvbXBvc2VkUmVmcyguLi5yZWZzKSB7XG4gIHJldHVybiBSZWFjdC51c2VDYWxsYmFjayhjb21wb3NlUmVmcyguLi5yZWZzKSwgcmVmcyk7XG59XG5leHBvcnQge1xuICBjb21wb3NlUmVmcyxcbiAgdXNlQ29tcG9zZWRSZWZzXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/createContext.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  function Provider(props) {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  }\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  Provider.displayName = rootComponentName + \"Provider\";\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    function Provider(props) {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName][index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    }\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName][index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    Provider.displayName = rootComponentName + \"Provider\";\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dialog/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger),\n/* harmony export */   Overlay: () => (/* binding */ Overlay),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   WarningProvider: () => (/* binding */ WarningProvider),\n/* harmony export */   createDialogScope: () => (/* binding */ createDialogScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Close,Content,Description,Dialog,DialogClose,DialogContent,DialogDescription,DialogOverlay,DialogPortal,DialogTitle,DialogTrigger,Overlay,Portal,Root,Title,Trigger,WarningProvider,createDialogScope auto */ // packages/react/dialog/src/Dialog.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar DIALOG_NAME = \"Dialog\";\nvar [createDialogContext, createDialogScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(DIALOG_NAME);\nvar [DialogProvider, useDialogContext] = createDialogContext(DIALOG_NAME);\nvar Dialog = (props)=>{\n    const { __scopeDialog, children, open: openProp, defaultOpen, onOpenChange, modal = true } = props;\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogProvider, {\n        scope: __scopeDialog,\n        triggerRef,\n        contentRef,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        titleId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        descriptionId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenChange: setOpen,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setOpen((prevOpen)=>!prevOpen), [\n            setOpen\n        ]),\n        modal,\n        children\n    });\n};\nDialog.displayName = DIALOG_NAME;\nvar TRIGGER_NAME = \"DialogTrigger\";\nvar DialogTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.triggerRef);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n});\nDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DialogPortal\";\nvar [PortalProvider, usePortalContext] = createDialogContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar DialogPortal = (props)=>{\n    const { __scopeDialog, forceMount, children, container } = props;\n    const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeDialog,\n        forceMount,\n        children: react__WEBPACK_IMPORTED_MODULE_0__.Children.map(children, (child)=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n                present: forceMount || context.open,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                    asChild: true,\n                    container,\n                    children: child\n                })\n            }))\n    });\n};\nDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"DialogOverlay\";\nvar DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogOverlayImpl, {\n            ...overlayProps,\n            ref: forwardedRef\n        })\n    }) : null;\n});\nDialogOverlay.displayName = OVERLAY_NAME;\nvar DialogOverlayImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return(// Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n    // ie. when `Overlay` and `Content` are siblings\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot,\n        allowPinchZoom: true,\n        shards: [\n            context.contentRef\n        ],\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.div, {\n            \"data-state\": getState(context.open),\n            ...overlayProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"auto\",\n                ...overlayProps.style\n            }\n        })\n    }));\n});\nvar CONTENT_NAME = \"DialogContent\";\nvar DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nDialogContent.displayName = CONTENT_NAME;\nvar DialogContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.contentRef, contentRef);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = contentRef.current;\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_12__.hideOthers)(content);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n            event.preventDefault();\n            context.triggerRef.current?.focus();\n        }),\n        onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            if (isRightClick) event.preventDefault();\n        }),\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault())\n    });\n});\nvar DialogContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            props.onCloseAutoFocus?.(event);\n            if (!event.defaultPrevented) {\n                if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            props.onInteractOutside?.(event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = context.triggerRef.current?.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n});\nvar DialogContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, contentRef);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__.FocusScope, {\n                asChild: true,\n                loop: true,\n                trapped: trapFocus,\n                onMountAutoFocus: onOpenAutoFocus,\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__.DismissableLayer, {\n                    role: \"dialog\",\n                    id: context.contentId,\n                    \"aria-describedby\": context.descriptionId,\n                    \"aria-labelledby\": context.titleId,\n                    \"data-state\": getState(context.open),\n                    ...contentProps,\n                    ref: composedRefs,\n                    onDismiss: ()=>context.onOpenChange(false)\n                })\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TitleWarning, {\n                        titleId: context.titleId\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionWarning, {\n                        contentRef,\n                        descriptionId: context.descriptionId\n                    })\n                ]\n            })\n        ]\n    });\n});\nvar TITLE_NAME = \"DialogTitle\";\nvar DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.h2, {\n        id: context.titleId,\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"DialogDescription\";\nvar DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.p, {\n        id: context.descriptionId,\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nDialogDescription.displayName = DESCRIPTION_NAME;\nvar CLOSE_NAME = \"DialogClose\";\nvar DialogClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n});\nDialogClose.displayName = CLOSE_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar TITLE_WARNING_NAME = \"DialogTitleWarning\";\nvar [WarningProvider, useWarningContext] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContext)(TITLE_WARNING_NAME, {\n    contentName: CONTENT_NAME,\n    titleName: TITLE_NAME,\n    docsSlug: \"dialog\"\n});\nvar TitleWarning = ({ titleId })=>{\n    const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n    const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (titleId) {\n            const hasTitle = document.getElementById(titleId);\n            if (!hasTitle) console.error(MESSAGE);\n        }\n    }, [\n        MESSAGE,\n        titleId\n    ]);\n    return null;\n};\nvar DESCRIPTION_WARNING_NAME = \"DialogDescriptionWarning\";\nvar DescriptionWarning = ({ contentRef, descriptionId })=>{\n    const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n    const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const describedById = contentRef.current?.getAttribute(\"aria-describedby\");\n        if (descriptionId && describedById) {\n            const hasDescription = document.getElementById(descriptionId);\n            if (!hasDescription) console.warn(MESSAGE);\n        }\n    }, [\n        MESSAGE,\n        contentRef,\n        descriptionId\n    ]);\n    return null;\n};\nvar Root = Dialog;\nvar Trigger = DialogTrigger;\nvar Portal = DialogPortal;\nvar Overlay = DialogOverlay;\nvar Content = DialogContent;\nvar Title = DialogTitle;\nvar Description = DialogDescription;\nvar Close = DialogClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // packages/react/dismissable-layer/src/DismissableLayer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event)=>{\n        const target = event.target;\n        const isPointerDownOnBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n        onPointerDownOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event)=>{\n        const target = event.target;\n        const isFocusInBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (isFocusInBranch) return;\n        onFocusOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)((event)=>{\n        const isHighestLayer = index === context.layers.size - 1;\n        if (!isHighestLayer) return;\n        onEscapeKeyDown?.(event);\n        if (!event.defaultPrevented && onDismiss) {\n            event.preventDefault();\n            onDismiss();\n        }\n    }, ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!node) return;\n        if (disableOutsidePointerEvents) {\n            if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                ownerDocument.body.style.pointerEvents = \"none\";\n            }\n            context.layersWithOutsidePointerEventsDisabled.add(node);\n        }\n        context.layers.add(node);\n        dispatchUpdate();\n        return ()=>{\n            if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n            }\n        };\n    }, [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (!node) return;\n            context.layers.delete(node);\n            context.layersWithOutsidePointerEventsDisabled.delete(node);\n            dispatchUpdate();\n        };\n    }, [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleUpdate = ()=>force({});\n        document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n        return ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const node = ref.current;\n        if (node) {\n            context.branches.add(node);\n            return ()=>{\n                context.branches.delete(node);\n            };\n        }\n    }, [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handlePointerDown = (event)=>{\n            if (event.target && !isPointerInsideReactTreeRef.current) {\n                let handleAndDispatchPointerDownOutsideEvent2 = function() {\n                    handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                        discrete: true\n                    });\n                };\n                var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                const eventDetail = {\n                    originalEvent: event\n                };\n                if (event.pointerType === \"touch\") {\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                    ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                        once: true\n                    });\n                } else {\n                    handleAndDispatchPointerDownOutsideEvent2();\n                }\n            } else {\n                ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n            }\n            isPointerInsideReactTreeRef.current = false;\n        };\n        const timerId = window.setTimeout(()=>{\n            ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n        }, 0);\n        return ()=>{\n            window.clearTimeout(timerId);\n            ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n        };\n    }, [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleFocus = (event)=>{\n            if (event.target && !isFocusInsideReactTreeRef.current) {\n                const eventDetail = {\n                    originalEvent: event\n                };\n                handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                    discrete: false\n                });\n            }\n        };\n        ownerDocument.addEventListener(\"focusin\", handleFocus);\n        return ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus);\n    }, [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-guards/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGuards: () => (/* binding */ FocusGuards),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   useFocusGuards: () => (/* binding */ useFocusGuards)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ FocusGuards,Root,useFocusGuards auto */ // packages/react/focus-guards/src/FocusGuards.tsx\n\nvar count = 0;\nfunction FocusGuards(props) {\n    useFocusGuards();\n    return props.children;\n}\nfunction useFocusGuards() {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n        document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n        document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n        count++;\n        return ()=>{\n            if (count === 1) {\n                document.querySelectorAll(\"[data-radix-focus-guard]\").forEach((node)=>node.remove());\n            }\n            count--;\n        };\n    }, []);\n}\nfunction createFocusGuard() {\n    const element = document.createElement(\"span\");\n    element.setAttribute(\"data-radix-focus-guard\", \"\");\n    element.tabIndex = 0;\n    element.style.cssText = \"outline: none; opacity: 0; position: fixed; pointer-events: none\";\n    return element;\n}\nvar Root = FocusGuards;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-scope/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ FocusScope),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ FocusScope,Root auto */ // packages/react/focus-scope/src/FocusScope.tsx\n\n\n\n\n\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { loop = false, trapped = false, onMountAutoFocus: onMountAutoFocusProp, onUnmountAutoFocus: onUnmountAutoFocusProp, ...scopeProps } = props;\n    const [container, setContainer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const onMountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onMountAutoFocusProp);\n    const onUnmountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onUnmountAutoFocusProp);\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContainer(node));\n    const focusScope = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        paused: false,\n        pause () {\n            this.paused = true;\n        },\n        resume () {\n            this.paused = false;\n        }\n    }).current;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (trapped) {\n            let handleFocusIn2 = function(event) {\n                if (focusScope.paused || !container) return;\n                const target = event.target;\n                if (container.contains(target)) {\n                    lastFocusedElementRef.current = target;\n                } else {\n                    focus(lastFocusedElementRef.current, {\n                        select: true\n                    });\n                }\n            }, handleFocusOut2 = function(event) {\n                if (focusScope.paused || !container) return;\n                const relatedTarget = event.relatedTarget;\n                if (relatedTarget === null) return;\n                if (!container.contains(relatedTarget)) {\n                    focus(lastFocusedElementRef.current, {\n                        select: true\n                    });\n                }\n            }, handleMutations2 = function(mutations) {\n                const focusedElement = document.activeElement;\n                if (focusedElement !== document.body) return;\n                for (const mutation of mutations){\n                    if (mutation.removedNodes.length > 0) focus(container);\n                }\n            };\n            var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n            document.addEventListener(\"focusin\", handleFocusIn2);\n            document.addEventListener(\"focusout\", handleFocusOut2);\n            const mutationObserver = new MutationObserver(handleMutations2);\n            if (container) mutationObserver.observe(container, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                document.removeEventListener(\"focusin\", handleFocusIn2);\n                document.removeEventListener(\"focusout\", handleFocusOut2);\n                mutationObserver.disconnect();\n            };\n        }\n    }, [\n        trapped,\n        container,\n        focusScope.paused\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (container) {\n            focusScopesStack.add(focusScope);\n            const previouslyFocusedElement = document.activeElement;\n            const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n            if (!hasFocusedCandidate) {\n                const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n                container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                container.dispatchEvent(mountEvent);\n                if (!mountEvent.defaultPrevented) {\n                    focusFirst(removeLinks(getTabbableCandidates(container)), {\n                        select: true\n                    });\n                    if (document.activeElement === previouslyFocusedElement) {\n                        focus(container);\n                    }\n                }\n            }\n            return ()=>{\n                container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                setTimeout(()=>{\n                    const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n                    container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                    container.dispatchEvent(unmountEvent);\n                    if (!unmountEvent.defaultPrevented) {\n                        focus(previouslyFocusedElement ?? document.body, {\n                            select: true\n                        });\n                    }\n                    container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                    focusScopesStack.remove(focusScope);\n                }, 0);\n            };\n        }\n    }, [\n        container,\n        onMountAutoFocus,\n        onUnmountAutoFocus,\n        focusScope\n    ]);\n    const handleKeyDown = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        if (!loop && !trapped) return;\n        if (focusScope.paused) return;\n        const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n        const focusedElement = document.activeElement;\n        if (isTabKey && focusedElement) {\n            const container2 = event.currentTarget;\n            const [first, last] = getTabbableEdges(container2);\n            const hasTabbableElementsInside = first && last;\n            if (!hasTabbableElementsInside) {\n                if (focusedElement === container2) event.preventDefault();\n            } else {\n                if (!event.shiftKey && focusedElement === last) {\n                    event.preventDefault();\n                    if (loop) focus(first, {\n                        select: true\n                    });\n                } else if (event.shiftKey && focusedElement === first) {\n                    event.preventDefault();\n                    if (loop) focus(last, {\n                        select: true\n                    });\n                }\n            }\n        }\n    }, [\n        loop,\n        trapped,\n        focusScope.paused\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        tabIndex: -1,\n        ...scopeProps,\n        ref: composedRefs,\n        onKeyDown: handleKeyDown\n    });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n    const previouslyFocusedElement = document.activeElement;\n    for (const candidate of candidates){\n        focus(candidate, {\n            select\n        });\n        if (document.activeElement !== previouslyFocusedElement) return;\n    }\n}\nfunction getTabbableEdges(container) {\n    const candidates = getTabbableCandidates(container);\n    const first = findVisible(candidates, container);\n    const last = findVisible(candidates.reverse(), container);\n    return [\n        first,\n        last\n    ];\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction findVisible(elements, container) {\n    for (const element of elements){\n        if (!isHidden(element, {\n            upTo: container\n        })) return element;\n    }\n}\nfunction isHidden(node, { upTo }) {\n    if (getComputedStyle(node).visibility === \"hidden\") return true;\n    while(node){\n        if (upTo !== void 0 && node === upTo) return false;\n        if (getComputedStyle(node).display === \"none\") return true;\n        node = node.parentElement;\n    }\n    return false;\n}\nfunction isSelectableInput(element) {\n    return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n    if (element && element.focus) {\n        const previouslyFocusedElement = document.activeElement;\n        element.focus({\n            preventScroll: true\n        });\n        if (element !== previouslyFocusedElement && isSelectableInput(element) && select) element.select();\n    }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n    let stack = [];\n    return {\n        add (focusScope) {\n            const activeFocusScope = stack[0];\n            if (focusScope !== activeFocusScope) {\n                activeFocusScope?.pause();\n            }\n            stack = arrayRemove(stack, focusScope);\n            stack.unshift(focusScope);\n        },\n        remove (focusScope) {\n            stack = arrayRemove(stack, focusScope);\n            stack[0]?.resume();\n        }\n    };\n}\nfunction arrayRemove(array, item) {\n    const updatedArray = [\n        ...array\n    ];\n    const index = updatedArray.indexOf(item);\n    if (index !== -1) {\n        updatedArray.splice(index, 1);\n    }\n    return updatedArray;\n}\nfunction removeLinks(items) {\n    return items.filter((item)=>item.tagName !== \"A\");\n}\nvar Root = FocusScope;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@radix-ui/react-id/dist/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useId: () => (/* binding */ useId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/id/src/id.tsx\n\n\nvar useReactId = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\"useId\".toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(useReactId());\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWlkL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUMrQjtBQUNxQztBQUNwRSxpQkFBaUIseUxBQUs7QUFDdEI7QUFDQTtBQUNBLHNCQUFzQiwyQ0FBYztBQUNwQyxFQUFFLGtGQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILDJDQUEyQyxHQUFHO0FBQzlDO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3RoZS1jYW52YXMvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWlkL2Rpc3QvaW5kZXgubWpzPzBhY2UiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvaWQvc3JjL2lkLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0XCI7XG52YXIgdXNlUmVhY3RJZCA9IFJlYWN0W1widXNlSWRcIi50b1N0cmluZygpXSB8fCAoKCkgPT4gdm9pZCAwKTtcbnZhciBjb3VudCA9IDA7XG5mdW5jdGlvbiB1c2VJZChkZXRlcm1pbmlzdGljSWQpIHtcbiAgY29uc3QgW2lkLCBzZXRJZF0gPSBSZWFjdC51c2VTdGF0ZSh1c2VSZWFjdElkKCkpO1xuICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghZGV0ZXJtaW5pc3RpY0lkKSBzZXRJZCgocmVhY3RJZCkgPT4gcmVhY3RJZCA/PyBTdHJpbmcoY291bnQrKykpO1xuICB9LCBbZGV0ZXJtaW5pc3RpY0lkXSk7XG4gIHJldHVybiBkZXRlcm1pbmlzdGljSWQgfHwgKGlkID8gYHJhZGl4LSR7aWR9YCA6IFwiXCIpO1xufVxuZXhwb3J0IHtcbiAgdXNlSWRcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // packages/react/portal/src/Portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>setMounted(true), []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence auto */ // packages/react/presence/src/Presence.tsx\n\n\n\n\n// packages/react/presence/src/useStateMachine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// packages/react/presence/src/Presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({});\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>{\n        if (node) {\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node && isCurrentAnimation) {\n                    react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>send(\"ANIMATION_END\"));\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            if (node2) stylesRef.current = getComputedStyle(node2);\n            setNode(node2);\n        }, [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/primitive/src/Primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-separator/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-separator/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/separator/src/Separator.tsx\n\n\n\nvar NAME = \"Separator\";\nvar DEFAULT_ORIENTATION = \"horizontal\";\nvar ORIENTATIONS = [\"horizontal\", \"vertical\"];\nvar Separator = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { decorative, orientation: orientationProp = DEFAULT_ORIENTATION, ...domProps } = props;\n  const orientation = isValidOrientation(orientationProp) ? orientationProp : DEFAULT_ORIENTATION;\n  const ariaOrientation = orientation === \"vertical\" ? orientation : void 0;\n  const semanticProps = decorative ? { role: \"none\" } : { \"aria-orientation\": ariaOrientation, role: \"separator\" };\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.div,\n    {\n      \"data-orientation\": orientation,\n      ...semanticProps,\n      ...domProps,\n      ref: forwardedRef\n    }\n  );\n});\nSeparator.displayName = NAME;\nfunction isValidOrientation(orientation) {\n  return ORIENTATIONS.includes(orientation);\n}\nvar Root = Separator;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-separator/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/slot/src/Slot.tsx\n\n\n\nvar Slot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n        return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      // @ts-ignore\n      ref: forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n};\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/useCallbackRef.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZi9kaXN0L2luZGV4Lm1qcz8yN2VjIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1jYWxsYmFjay1yZWYvc3JjL3VzZUNhbGxiYWNrUmVmLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VDYWxsYmFja1JlZihjYWxsYmFjaykge1xuICBjb25zdCBjYWxsYmFja1JlZiA9IFJlYWN0LnVzZVJlZihjYWxsYmFjayk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2FsbGJhY2tSZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKC4uLmFyZ3MpID0+IGNhbGxiYWNrUmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlQ2FsbGJhY2tSZWZcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-controllable-state/src/useControllableState.tsx\n\n\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  }\n}) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onChange);\n  const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue;\n        const value2 = typeof nextValue === \"function\" ? setter(prop) : nextValue;\n        if (value2 !== prop) handleChange(value2);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const uncontrolledState = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  const handleChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onChange);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n  return uncontrolledState;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/useEscapeKeydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUNtQztBQUNsRTtBQUNBLDBCQUEwQixnRkFBYztBQUN4QyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsZUFBZTtBQUM5RSwrRUFBK0UsZUFBZTtBQUM5RixHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWVzY2FwZS1rZXlkb3duL2Rpc3QvaW5kZXgubWpzP2RhYTEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWVzY2FwZS1rZXlkb3duL3NyYy91c2VFc2NhcGVLZXlkb3duLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VDYWxsYmFja1JlZiB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZlwiO1xuZnVuY3Rpb24gdXNlRXNjYXBlS2V5ZG93bihvbkVzY2FwZUtleURvd25Qcm9wLCBvd25lckRvY3VtZW50ID0gZ2xvYmFsVGhpcz8uZG9jdW1lbnQpIHtcbiAgY29uc3Qgb25Fc2NhcGVLZXlEb3duID0gdXNlQ2FsbGJhY2tSZWYob25Fc2NhcGVLZXlEb3duUHJvcCk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlS2V5RG93biA9IChldmVudCkgPT4ge1xuICAgICAgaWYgKGV2ZW50LmtleSA9PT0gXCJFc2NhcGVcIikge1xuICAgICAgICBvbkVzY2FwZUtleURvd24oZXZlbnQpO1xuICAgICAgfVxuICAgIH07XG4gICAgb3duZXJEb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKFwia2V5ZG93blwiLCBoYW5kbGVLZXlEb3duLCB7IGNhcHR1cmU6IHRydWUgfSk7XG4gICAgcmV0dXJuICgpID0+IG93bmVyRG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgaGFuZGxlS2V5RG93biwgeyBjYXB0dXJlOiB0cnVlIH0pO1xuICB9LCBbb25Fc2NhcGVLZXlEb3duLCBvd25lckRvY3VtZW50XSk7XG59XG5leHBvcnQge1xuICB1c2VFc2NhcGVLZXlkb3duXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/useLayoutEffect.tsx\n\nvar useLayoutEffect2 = Boolean(globalThis?.document) ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsdURBQXVELGtEQUFxQjtBQUM1RTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdC9kaXN0L2luZGV4Lm1qcz9lZjAwIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1sYXlvdXQtZWZmZWN0L3NyYy91c2VMYXlvdXRFZmZlY3QudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VMYXlvdXRFZmZlY3QyID0gQm9vbGVhbihnbG9iYWxUaGlzPy5kb2N1bWVudCkgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiAoKSA9PiB7XG59O1xuZXhwb3J0IHtcbiAgdXNlTGF5b3V0RWZmZWN0MiBhcyB1c2VMYXlvdXRFZmZlY3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ })

};
;