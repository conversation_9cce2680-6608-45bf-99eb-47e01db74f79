"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx":
/*!********************************************************************!*\
  !*** ./src/features/editor/components/template-config-sidebar.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateConfigSidebar: function() { return /* binding */ TemplateConfigSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/projects/api/use-update-template-config */ \"(app-pages-browser)/./src/features/projects/api/use-update-template-config.ts\");\n/* __next_internal_client_entry_do_not_use__ TemplateConfigSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst TemplateConfigSidebar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool, projectId } = param;\n    _s();\n    const [editableLayers, setEditableLayers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCustomizable, setIsCustomizable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateTemplateConfig = (0,_features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__.useUpdateTemplateConfig)(projectId || \"\");\n    const onClose = ()=>{\n        onChangeActiveTool(\"select\");\n    };\n    const canvas = editor === null || editor === void 0 ? void 0 : editor.canvas;\n    const selectedObjects = (editor === null || editor === void 0 ? void 0 : editor.selectedObjects) || [];\n    // Load existing editable layers from canvas objects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!canvas) return;\n        const layers = [];\n        canvas.getObjects().forEach((obj)=>{\n            const editableObj = obj;\n            if (editableObj.isEditable) {\n                layers.push({\n                    id: editableObj.id || \"\",\n                    type: editableObj.editableType || \"text\",\n                    name: editableObj.editableName || \"Unnamed Layer\",\n                    originalValue: editableObj.type === \"textbox\" ? editableObj.text : \"\",\n                    placeholder: editableObj.editablePlaceholder,\n                    constraints: editableObj.editableConstraints\n                });\n            }\n        });\n        setEditableLayers(layers);\n        setIsCustomizable(layers.length > 0);\n    }, [\n        canvas\n    ]);\n    const makeLayerEditable = (type)=>{\n        if (!canvas || selectedObjects.length === 0) return;\n        const selectedObject = selectedObjects[0];\n        // Validate object type\n        if (type === \"text\" && selectedObject.type !== \"textbox\") {\n            alert(\"Please select a text object to make it editable\");\n            return;\n        }\n        if (type === \"image\" && ![\n            \"image\",\n            \"rect\",\n            \"circle\"\n        ].includes(selectedObject.type || \"\")) {\n            alert(\"Please select an image or shape to make it editable\");\n            return;\n        }\n        // Mark object as editable\n        selectedObject.isEditable = true;\n        selectedObject.editableType = type;\n        selectedObject.editableName = \"\".concat(type === \"text\" ? \"Text\" : \"Image\", \" \").concat(editableLayers.length + 1);\n        if (type === \"text\") {\n            selectedObject.editablePlaceholder = \"Enter your text here...\";\n        }\n        // Add to editable layers list\n        const newLayer = {\n            id: selectedObject.id || \"layer_\".concat(Date.now()),\n            type,\n            name: selectedObject.editableName,\n            originalValue: type === \"text\" ? selectedObject.text : \"\",\n            placeholder: selectedObject.editablePlaceholder,\n            constraints: {\n                maxLength: type === \"text\" ? 100 : undefined,\n                allowedFormats: type === \"image\" ? [\n                    \"jpg\",\n                    \"jpeg\",\n                    \"png\",\n                    \"gif\"\n                ] : undefined,\n                maxFileSize: type === \"image\" ? 5 * 1024 * 1024 : undefined\n            }\n        };\n        // Assign ID if not exists\n        if (!selectedObject.id) {\n            selectedObject.id = newLayer.id;\n        }\n        setEditableLayers([\n            ...editableLayers,\n            newLayer\n        ]);\n        setIsCustomizable(true);\n        canvas.renderAll();\n    };\n    const removeEditableLayer = (layerId)=>{\n        // Find and update the canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.isEditable = false;\n            delete canvasObject.editableType;\n            delete canvasObject.editableName;\n            delete canvasObject.editablePlaceholder;\n            delete canvasObject.editableConstraints;\n        }\n        // Remove from layers list\n        const updatedLayers = editableLayers.filter((layer)=>layer.id !== layerId);\n        setEditableLayers(updatedLayers);\n        setIsCustomizable(updatedLayers.length > 0);\n        canvas === null || canvas === void 0 ? void 0 : canvas.renderAll();\n    };\n    const updateLayerName = (layerId, name)=>{\n        const updatedLayers = editableLayers.map((layer)=>layer.id === layerId ? {\n                ...layer,\n                name\n            } : layer);\n        setEditableLayers(updatedLayers);\n        // Update canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.editableName = name;\n        }\n    };\n    const updateLayerPlaceholder = (layerId, placeholder)=>{\n        const updatedLayers = editableLayers.map((layer)=>layer.id === layerId ? {\n                ...layer,\n                placeholder\n            } : layer);\n        setEditableLayers(updatedLayers);\n        // Update canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.editablePlaceholder = placeholder;\n        }\n    };\n    const saveTemplateConfig = ()=>{\n        if (!projectId) {\n            alert(\"Project ID is required to save template configuration\");\n            return;\n        }\n        updateTemplateConfig.mutate({\n            isCustomizable,\n            editableLayers: JSON.stringify(editableLayers)\n        });\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"bg-white relative border-r z-[40] w-[360px] h-full flex flex-col\", activeTool === \"template-config\" ? \"visible\" : \"hidden\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: \"Template Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onClose,\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 mt-1\",\n                        children: \"Configure which elements users can customize\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__.ScrollArea, {\n                className: \"flex-1 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Template Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"customizable\",\n                                                className: \"text-sm\",\n                                                children: \"Make Customizable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_5__.Switch, {\n                                                id: \"customizable\",\n                                                checked: isCustomizable,\n                                                onCheckedChange: setIsCustomizable\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-2\",\n                                        children: \"Allow public users to customize this template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm\",\n                                    children: \"Add Editable Elements\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full justify-start\",\n                                        onClick: ()=>makeLayerEditable(\"text\"),\n                                        disabled: selectedObjects.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make Text Editable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full justify-start\",\n                                        onClick: ()=>makeLayerEditable(\"image\"),\n                                        disabled: selectedObjects.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make Image Replaceable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    selectedObjects.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"Select an element on the canvas first\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, undefined),\n                    editableLayers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm\",\n                                    children: [\n                                        \"Editable Elements (\",\n                                        editableLayers.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-3\",\n                                children: editableLayers.map((layer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                        variant: layer.type === \"text\" ? \"default\" : \"secondary\",\n                                                        children: [\n                                                            layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 25\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            layer.type\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>removeEditableLayer(layer.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-xs\",\n                                                                children: \"Display Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: layer.name,\n                                                                onChange: (e)=>updateLayerName(layer.id, e.target.value),\n                                                                className: \"h-8 text-sm\",\n                                                                placeholder: \"e.g., Your Name, Profile Photo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    layer.type === \"text\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-xs\",\n                                                                children: \"Placeholder Text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: layer.placeholder || \"\",\n                                                                onChange: (e)=>updateLayerPlaceholder(layer.id, e.target.value),\n                                                                className: \"h-8 text-sm\",\n                                                                placeholder: \"Enter placeholder text...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, layer.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: saveTemplateConfig,\n                    className: \"w-full\",\n                    disabled: !isCustomizable || editableLayers.length === 0,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"h-4 w-4 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, undefined),\n                        \"Save Template Config\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 332,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateConfigSidebar, \"xbSYcelWy/Jd01PO3bPHFWr+Lo4=\", false, function() {\n    return [\n        _features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__.useUpdateTemplateConfig\n    ];\n});\n_c = TemplateConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TemplateConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx\n"));

/***/ })

});