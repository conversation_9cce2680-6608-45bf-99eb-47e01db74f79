hook.js:608 Warning: Extra attributes from the server: webcrx Error Component Stack
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
:3002/api/projects/public?page=1&limit=12:1  Failed to load resource: net::ERR_NETWORK_IO_SUSPENDED
hook.js:608 Warning: Chrome: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at Chrome (Chrome.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at Editor (editor.tsx:40:26)
    at EditorProjectIdPage (page.tsx:18:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
hook.js:608 Warning: Checkboard: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at Checkboard (Checkboard.js:16:20)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at Chrome (Chrome.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at Editor (editor.tsx:40:26)
    at EditorProjectIdPage (page.tsx:18:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
hook.js:608 Warning: Circle: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at Circle (Circle.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at Editor (editor.tsx:40:26)
    at EditorProjectIdPage (page.tsx:18:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
hook.js:608 Warning: CircleSwatch: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at CircleSwatch (CircleSwatch.js:15:20)
    at span (<anonymous>)
    at Hover (hover.js:33:7)
    at div (<anonymous>)
    at Circle (Circle.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at Editor (editor.tsx:40:26)
    at EditorProjectIdPage (page.tsx:18:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
