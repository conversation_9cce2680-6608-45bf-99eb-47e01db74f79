hook.js:608 Warning: Extra attributes from the server: webcrx Error Component Stack
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
printWarning @ react-dom.development.js:94
error @ react-dom.development.js:68
warnForExtraAttributes @ react-dom.development.js:32731
diffHydratedProperties @ react-dom.development.js:35117
hydrateInstance @ react-dom.development.js:36127
prepareToHydrateHostInstance @ react-dom.development.js:7246
completeWork @ react-dom.development.js:19725
completeUnitOfWork @ react-dom.development.js:25963
performUnitOfWork @ react-dom.development.js:25759
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performConcurrentWorkOnRoot @ react-dom.development.js:24504
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
hook.js:608 Warning: Chrome: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at Chrome (Chrome.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at Editor (editor.tsx:40:26)
    at EditorProjectIdPage (page.tsx:18:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
printWarning @ react-dom.development.js:94
error @ react-dom.development.js:68
validateFunctionComponentInDev @ react-dom.development.js:16978
mountIndeterminateComponent @ react-dom.development.js:16941
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
setTimeout
scheduleFn @ notifyManager.js:16
flush @ notifyManager.js:53
batch @ notifyManager.js:28
#dispatch @ query.js:341
setData @ query.js:52
onSuccess @ query.js:244
resolve @ retryer.js:58
Promise.then
run @ retryer.js:97
start @ retryer.js:134
fetch @ query.js:271
#executeFetch @ queryObserver.js:172
onSubscribe @ queryObserver.js:48
subscribe @ subscribable.js:13
eval @ useBaseQuery.js:41
subscribeToStore @ react-dom.development.js:12033
commitHookEffectListMount @ react-dom.development.js:21102
commitHookPassiveMountEffects @ react-dom.development.js:23154
commitPassiveMountOnFiber @ react-dom.development.js:23259
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23267
commitPassiveMountEffects @ react-dom.development.js:23225
flushPassiveEffectsImpl @ react-dom.development.js:26497
flushPassiveEffects @ react-dom.development.js:26438
eval @ react-dom.development.js:26172
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
hook.js:608 Warning: Checkboard: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at Checkboard (Checkboard.js:16:20)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at Chrome (Chrome.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at Editor (editor.tsx:40:26)
    at EditorProjectIdPage (page.tsx:18:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
printWarning @ react-dom.development.js:94
error @ react-dom.development.js:68
validateFunctionComponentInDev @ react-dom.development.js:16978
mountIndeterminateComponent @ react-dom.development.js:16941
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
setTimeout
scheduleFn @ notifyManager.js:16
flush @ notifyManager.js:53
batch @ notifyManager.js:28
#dispatch @ query.js:341
setData @ query.js:52
onSuccess @ query.js:244
resolve @ retryer.js:58
Promise.then
run @ retryer.js:97
start @ retryer.js:134
fetch @ query.js:271
#executeFetch @ queryObserver.js:172
onSubscribe @ queryObserver.js:48
subscribe @ subscribable.js:13
eval @ useBaseQuery.js:41
subscribeToStore @ react-dom.development.js:12033
commitHookEffectListMount @ react-dom.development.js:21102
commitHookPassiveMountEffects @ react-dom.development.js:23154
commitPassiveMountOnFiber @ react-dom.development.js:23259
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23267
commitPassiveMountEffects @ react-dom.development.js:23225
flushPassiveEffectsImpl @ react-dom.development.js:26497
flushPassiveEffects @ react-dom.development.js:26438
eval @ react-dom.development.js:26172
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
hook.js:608 Warning: Circle: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at Circle (Circle.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at Editor (editor.tsx:40:26)
    at EditorProjectIdPage (page.tsx:18:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
printWarning @ react-dom.development.js:94
error @ react-dom.development.js:68
validateFunctionComponentInDev @ react-dom.development.js:16978
mountIndeterminateComponent @ react-dom.development.js:16941
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
setTimeout
scheduleFn @ notifyManager.js:16
flush @ notifyManager.js:53
batch @ notifyManager.js:28
#dispatch @ query.js:341
setData @ query.js:52
onSuccess @ query.js:244
resolve @ retryer.js:58
Promise.then
run @ retryer.js:97
start @ retryer.js:134
fetch @ query.js:271
#executeFetch @ queryObserver.js:172
onSubscribe @ queryObserver.js:48
subscribe @ subscribable.js:13
eval @ useBaseQuery.js:41
subscribeToStore @ react-dom.development.js:12033
commitHookEffectListMount @ react-dom.development.js:21102
commitHookPassiveMountEffects @ react-dom.development.js:23154
commitPassiveMountOnFiber @ react-dom.development.js:23259
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23267
commitPassiveMountEffects @ react-dom.development.js:23225
flushPassiveEffectsImpl @ react-dom.development.js:26497
flushPassiveEffects @ react-dom.development.js:26438
eval @ react-dom.development.js:26172
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
hook.js:608 Warning: CircleSwatch: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at CircleSwatch (CircleSwatch.js:15:20)
    at span (<anonymous>)
    at Hover (hover.js:33:7)
    at div (<anonymous>)
    at Circle (Circle.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at Editor (editor.tsx:40:26)
    at EditorProjectIdPage (page.tsx:18:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
printWarning @ react-dom.development.js:94
error @ react-dom.development.js:68
validateFunctionComponentInDev @ react-dom.development.js:16978
mountIndeterminateComponent @ react-dom.development.js:16941
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
setTimeout
scheduleFn @ notifyManager.js:16
flush @ notifyManager.js:53
batch @ notifyManager.js:28
#dispatch @ query.js:341
setData @ query.js:52
onSuccess @ query.js:244
resolve @ retryer.js:58
Promise.then
run @ retryer.js:97
start @ retryer.js:134
fetch @ query.js:271
#executeFetch @ queryObserver.js:172
onSubscribe @ queryObserver.js:48
subscribe @ subscribable.js:13
eval @ useBaseQuery.js:41
subscribeToStore @ react-dom.development.js:12033
commitHookEffectListMount @ react-dom.development.js:21102
commitHookPassiveMountEffects @ react-dom.development.js:23154
commitPassiveMountOnFiber @ react-dom.development.js:23259
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23267
commitPassiveMountEffects @ react-dom.development.js:23225
flushPassiveEffectsImpl @ react-dom.development.js:26497
flushPassiveEffects @ react-dom.development.js:26438
eval @ react-dom.development.js:26172
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
hook.js:608 Warning: Cannot update a component (`HotReload`) while rendering a different component (`TemplateConfigSidebar`). To locate the bad setState() call inside `TemplateConfigSidebar`, follow the stack trace as described in https://reactjs.org/link/setstate-in-render Error Component Stack
    at TemplateConfigSidebar (template-config-sidebar.tsx:35:41)
    at div (<anonymous>)
    at div (<anonymous>)
    at Editor (editor.tsx:40:26)
    at EditorProjectIdPage (page.tsx:18:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
printWarning @ react-dom.development.js:94
error @ react-dom.development.js:68
warnAboutRenderPhaseUpdatesInDEV @ react-dom.development.js:26990
scheduleUpdateOnFiber @ react-dom.development.js:24395
dispatchReducerAction @ react-dom.development.js:13001
eval @ hot-reloader-client.js:361
eval @ use-error-handler.js:62
invokeGuardedCallbackImpl @ react-dom.development.js:20614
invokeGuardedCallback @ react-dom.development.js:20689
beginWork @ react-dom.development.js:26949
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
use-editor.ts:618 Uncaught TypeError: Cannot destructure property 'defaultState' of 'param' as it is undefined.
    at useEditor (use-editor.ts:618:3)
    at TemplateConfigSidebar (template-config-sidebar.tsx:36:48)
    at renderWithHooks (react-dom.development.js:11121:18)
    at mountIndeterminateComponent (react-dom.development.js:16869:13)
    at beginWork$1 (react-dom.development.js:18458:16)
    at HTMLUnknownElement.callCallback (react-dom.development.js:20565:14)
    at Object.invokeGuardedCallbackImpl (react-dom.development.js:20614:16)
    at invokeGuardedCallback (react-dom.development.js:20689:29)
    at beginWork (react-dom.development.js:26949:7)
    at performUnitOfWork (react-dom.development.js:25748:12)
    at workLoopSync (react-dom.development.js:25464:5)
    at renderRootSync (react-dom.development.js:25419:7)
    at performSyncWorkOnRoot (react-dom.development.js:24887:20)
    at flushSyncWorkAcrossRoots_impl (react-dom.development.js:7758:13)
    at flushSyncWorkOnAllRoots (react-dom.development.js:7718:3)
    at processRootScheduleInMicrotask (react-dom.development.js:7863:3)
    at eval (react-dom.development.js:8034:7)
useEditor @ use-editor.ts:618
TemplateConfigSidebar @ template-config-sidebar.tsx:36
renderWithHooks @ react-dom.development.js:11121
mountIndeterminateComponent @ react-dom.development.js:16869
beginWork$1 @ react-dom.development.js:18458
callCallback @ react-dom.development.js:20565
invokeGuardedCallbackImpl @ react-dom.development.js:20614
invokeGuardedCallback @ react-dom.development.js:20689
beginWork @ react-dom.development.js:26949
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
redirect-boundary.js:57 Uncaught TypeError: Cannot destructure property 'defaultState' of 'param' as it is undefined.
    at useEditor (use-editor.ts:618:3)
    at TemplateConfigSidebar (template-config-sidebar.tsx:36:48)
    at renderWithHooks (react-dom.development.js:11121:18)
    at mountIndeterminateComponent (react-dom.development.js:16869:13)
    at beginWork$1 (react-dom.development.js:18458:16)
    at beginWork (react-dom.development.js:26927:14)
    at performUnitOfWork (react-dom.development.js:25748:12)
    at workLoopSync (react-dom.development.js:25464:5)
    at renderRootSync (react-dom.development.js:25419:7)
    at performSyncWorkOnRoot (react-dom.development.js:24887:20)
    at flushSyncWorkAcrossRoots_impl (react-dom.development.js:7758:13)
    at flushSyncWorkOnAllRoots (react-dom.development.js:7718:3)
    at processRootScheduleInMicrotask (react-dom.development.js:7863:3)
    at eval (react-dom.development.js:8034:7)
useEditor @ use-editor.ts:618
TemplateConfigSidebar @ template-config-sidebar.tsx:36
renderWithHooks @ react-dom.development.js:11121
mountIndeterminateComponent @ react-dom.development.js:16869
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
redirect-boundary.js:57 Uncaught TypeError: Cannot destructure property 'defaultState' of 'param' as it is undefined.
    at useEditor (use-editor.ts:618:3)
    at TemplateConfigSidebar (template-config-sidebar.tsx:36:48)
    at renderWithHooks (react-dom.development.js:11121:18)
    at mountIndeterminateComponent (react-dom.development.js:16869:13)
    at beginWork$1 (react-dom.development.js:18458:16)
    at beginWork (react-dom.development.js:26927:14)
    at performUnitOfWork (react-dom.development.js:25748:12)
    at workLoopSync (react-dom.development.js:25464:5)
    at renderRootSync (react-dom.development.js:25419:7)
    at performSyncWorkOnRoot (react-dom.development.js:24887:20)
    at flushSyncWorkAcrossRoots_impl (react-dom.development.js:7758:13)
    at flushSyncWorkOnAllRoots (react-dom.development.js:7718:3)
    at processRootScheduleInMicrotask (react-dom.development.js:7863:3)
    at eval (react-dom.development.js:8034:7)
useEditor @ use-editor.ts:618
TemplateConfigSidebar @ template-config-sidebar.tsx:36
renderWithHooks @ react-dom.development.js:11121
mountIndeterminateComponent @ react-dom.development.js:16869
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
redirect-boundary.js:57 Uncaught TypeError: Cannot destructure property 'defaultState' of 'param' as it is undefined.
    at useEditor (use-editor.ts:618:3)
    at TemplateConfigSidebar (template-config-sidebar.tsx:36:48)
    at renderWithHooks (react-dom.development.js:11121:18)
    at mountIndeterminateComponent (react-dom.development.js:16869:13)
    at beginWork$1 (react-dom.development.js:18458:16)
    at beginWork (react-dom.development.js:26927:14)
    at performUnitOfWork (react-dom.development.js:25748:12)
    at workLoopSync (react-dom.development.js:25464:5)
    at renderRootSync (react-dom.development.js:25419:7)
    at performSyncWorkOnRoot (react-dom.development.js:24887:20)
    at flushSyncWorkAcrossRoots_impl (react-dom.development.js:7758:13)
    at flushSyncWorkOnAllRoots (react-dom.development.js:7718:3)
    at processRootScheduleInMicrotask (react-dom.development.js:7863:3)
    at eval (react-dom.development.js:8034:7)
useEditor @ use-editor.ts:618
TemplateConfigSidebar @ template-config-sidebar.tsx:36
renderWithHooks @ react-dom.development.js:11121
mountIndeterminateComponent @ react-dom.development.js:16869
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
not-found-boundary.js:37 Uncaught TypeError: Cannot destructure property 'defaultState' of 'param' as it is undefined.
    at useEditor (use-editor.ts:618:3)
    at TemplateConfigSidebar (template-config-sidebar.tsx:36:48)
    at renderWithHooks (react-dom.development.js:11121:18)
    at mountIndeterminateComponent (react-dom.development.js:16869:13)
    at beginWork$1 (react-dom.development.js:18458:16)
    at beginWork (react-dom.development.js:26927:14)
    at performUnitOfWork (react-dom.development.js:25748:12)
    at workLoopSync (react-dom.development.js:25464:5)
    at renderRootSync (react-dom.development.js:25419:7)
    at performSyncWorkOnRoot (react-dom.development.js:24887:20)
    at flushSyncWorkAcrossRoots_impl (react-dom.development.js:7758:13)
    at flushSyncWorkOnAllRoots (react-dom.development.js:7718:3)
    at processRootScheduleInMicrotask (react-dom.development.js:7863:3)
    at eval (react-dom.development.js:8034:7)
useEditor @ use-editor.ts:618
TemplateConfigSidebar @ template-config-sidebar.tsx:36
renderWithHooks @ react-dom.development.js:11121
mountIndeterminateComponent @ react-dom.development.js:16869
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
redirect-boundary.js:57 Uncaught TypeError: Cannot destructure property 'defaultState' of 'param' as it is undefined.
    at useEditor (use-editor.ts:618:3)
    at TemplateConfigSidebar (template-config-sidebar.tsx:36:48)
    at renderWithHooks (react-dom.development.js:11121:18)
    at mountIndeterminateComponent (react-dom.development.js:16869:13)
    at beginWork$1 (react-dom.development.js:18458:16)
    at beginWork (react-dom.development.js:26927:14)
    at performUnitOfWork (react-dom.development.js:25748:12)
    at workLoopSync (react-dom.development.js:25464:5)
    at renderRootSync (react-dom.development.js:25419:7)
    at performSyncWorkOnRoot (react-dom.development.js:24887:20)
    at flushSyncWorkAcrossRoots_impl (react-dom.development.js:7758:13)
    at flushSyncWorkOnAllRoots (react-dom.development.js:7718:3)
    at processRootScheduleInMicrotask (react-dom.development.js:7863:3)
    at eval (react-dom.development.js:8034:7)
useEditor @ use-editor.ts:618
TemplateConfigSidebar @ template-config-sidebar.tsx:36
renderWithHooks @ react-dom.development.js:11121
mountIndeterminateComponent @ react-dom.development.js:16869
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
not-found-boundary.js:37 Uncaught TypeError: Cannot destructure property 'defaultState' of 'param' as it is undefined.
    at useEditor (use-editor.ts:618:3)
    at TemplateConfigSidebar (template-config-sidebar.tsx:36:48)
    at renderWithHooks (react-dom.development.js:11121:18)
    at mountIndeterminateComponent (react-dom.development.js:16869:13)
    at beginWork$1 (react-dom.development.js:18458:16)
    at beginWork (react-dom.development.js:26927:14)
    at performUnitOfWork (react-dom.development.js:25748:12)
    at workLoopSync (react-dom.development.js:25464:5)
    at renderRootSync (react-dom.development.js:25419:7)
    at performSyncWorkOnRoot (react-dom.development.js:24887:20)
    at flushSyncWorkAcrossRoots_impl (react-dom.development.js:7758:13)
    at flushSyncWorkOnAllRoots (react-dom.development.js:7718:3)
    at processRootScheduleInMicrotask (react-dom.development.js:7863:3)
    at eval (react-dom.development.js:8034:7)
useEditor @ use-editor.ts:618
TemplateConfigSidebar @ template-config-sidebar.tsx:36
renderWithHooks @ react-dom.development.js:11121
mountIndeterminateComponent @ react-dom.development.js:16869
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
use-editor.ts:618 Uncaught TypeError: Cannot destructure property 'defaultState' of 'param' as it is undefined.
    at useEditor (use-editor.ts:618:3)
    at TemplateConfigSidebar (template-config-sidebar.tsx:36:48)
    at renderWithHooks (react-dom.development.js:11121:18)
    at mountIndeterminateComponent (react-dom.development.js:16869:13)
    at beginWork$1 (react-dom.development.js:18458:16)
    at HTMLUnknownElement.callCallback (react-dom.development.js:20565:14)
    at Object.invokeGuardedCallbackImpl (react-dom.development.js:20614:16)
    at invokeGuardedCallback (react-dom.development.js:20689:29)
    at beginWork (react-dom.development.js:26949:7)
    at performUnitOfWork (react-dom.development.js:25748:12)
    at workLoopSync (react-dom.development.js:25464:5)
    at renderRootSync (react-dom.development.js:25419:7)
    at recoverFromConcurrentError (react-dom.development.js:24597:20)
    at performSyncWorkOnRoot (react-dom.development.js:24899:20)
    at flushSyncWorkAcrossRoots_impl (react-dom.development.js:7758:13)
    at flushSyncWorkOnAllRoots (react-dom.development.js:7718:3)
    at processRootScheduleInMicrotask (react-dom.development.js:7863:3)
    at eval (react-dom.development.js:8034:7)
useEditor @ use-editor.ts:618
TemplateConfigSidebar @ template-config-sidebar.tsx:36
renderWithHooks @ react-dom.development.js:11121
mountIndeterminateComponent @ react-dom.development.js:16869
beginWork$1 @ react-dom.development.js:18458
callCallback @ react-dom.development.js:20565
invokeGuardedCallbackImpl @ react-dom.development.js:20614
invokeGuardedCallback @ react-dom.development.js:20689
beginWork @ react-dom.development.js:26949
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
recoverFromConcurrentError @ react-dom.development.js:24597
performSyncWorkOnRoot @ react-dom.development.js:24899
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
redirect-boundary.js:57 Uncaught TypeError: Cannot destructure property 'defaultState' of 'param' as it is undefined.
    at useEditor (use-editor.ts:618:3)
    at TemplateConfigSidebar (template-config-sidebar.tsx:36:48)
    at renderWithHooks (react-dom.development.js:11121:18)
    at mountIndeterminateComponent (react-dom.development.js:16869:13)
    at beginWork$1 (react-dom.development.js:18458:16)
    at beginWork (react-dom.development.js:26927:14)
    at performUnitOfWork (react-dom.development.js:25748:12)
    at workLoopSync (react-dom.development.js:25464:5)
    at renderRootSync (react-dom.development.js:25419:7)
    at recoverFromConcurrentError (react-dom.development.js:24597:20)
    at performSyncWorkOnRoot (react-dom.development.js:24899:20)
    at flushSyncWorkAcrossRoots_impl (react-dom.development.js:7758:13)
    at flushSyncWorkOnAllRoots (react-dom.development.js:7718:3)
    at processRootScheduleInMicrotask (react-dom.development.js:7863:3)
    at eval (react-dom.development.js:8034:7)
useEditor @ use-editor.ts:618
TemplateConfigSidebar @ template-config-sidebar.tsx:36
renderWithHooks @ react-dom.development.js:11121
mountIndeterminateComponent @ react-dom.development.js:16869
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
recoverFromConcurrentError @ react-dom.development.js:24597
performSyncWorkOnRoot @ react-dom.development.js:24899
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
redirect-boundary.js:57 Uncaught TypeError: Cannot destructure property 'defaultState' of 'param' as it is undefined.
    at useEditor (use-editor.ts:618:3)
    at TemplateConfigSidebar (template-config-sidebar.tsx:36:48)
    at renderWithHooks (react-dom.development.js:11121:18)
    at mountIndeterminateComponent (react-dom.development.js:16869:13)
    at beginWork$1 (react-dom.development.js:18458:16)
    at beginWork (react-dom.development.js:26927:14)
    at performUnitOfWork (react-dom.development.js:25748:12)
    at workLoopSync (react-dom.development.js:25464:5)
    at renderRootSync (react-dom.development.js:25419:7)
    at recoverFromConcurrentError (react-dom.development.js:24597:20)
    at performSyncWorkOnRoot (react-dom.development.js:24899:20)
    at flushSyncWorkAcrossRoots_impl (react-dom.development.js:7758:13)
    at flushSyncWorkOnAllRoots (react-dom.development.js:7718:3)
    at processRootScheduleInMicrotask (react-dom.development.js:7863:3)
    at eval (react-dom.development.js:8034:7)
useEditor @ use-editor.ts:618
TemplateConfigSidebar @ template-config-sidebar.tsx:36
renderWithHooks @ react-dom.development.js:11121
mountIndeterminateComponent @ react-dom.development.js:16869
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
recoverFromConcurrentError @ react-dom.development.js:24597
performSyncWorkOnRoot @ react-dom.development.js:24899
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
redirect-boundary.js:57 Uncaught TypeError: Cannot destructure property 'defaultState' of 'param' as it is undefined.
    at useEditor (use-editor.ts:618:3)
    at TemplateConfigSidebar (template-config-sidebar.tsx:36:48)
    at renderWithHooks (react-dom.development.js:11121:18)
    at mountIndeterminateComponent (react-dom.development.js:16869:13)
    at beginWork$1 (react-dom.development.js:18458:16)
    at beginWork (react-dom.development.js:26927:14)
    at performUnitOfWork (react-dom.development.js:25748:12)
    at workLoopSync (react-dom.development.js:25464:5)
    at renderRootSync (react-dom.development.js:25419:7)
    at recoverFromConcurrentError (react-dom.development.js:24597:20)
    at performSyncWorkOnRoot (react-dom.development.js:24899:20)
    at flushSyncWorkAcrossRoots_impl (react-dom.development.js:7758:13)
    at flushSyncWorkOnAllRoots (react-dom.development.js:7718:3)
    at processRootScheduleInMicrotask (react-dom.development.js:7863:3)
    at eval (react-dom.development.js:8034:7)
useEditor @ use-editor.ts:618
TemplateConfigSidebar @ template-config-sidebar.tsx:36
renderWithHooks @ react-dom.development.js:11121
mountIndeterminateComponent @ react-dom.development.js:16869
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
recoverFromConcurrentError @ react-dom.development.js:24597
performSyncWorkOnRoot @ react-dom.development.js:24899
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
not-found-boundary.js:37 Uncaught TypeError: Cannot destructure property 'defaultState' of 'param' as it is undefined.
    at useEditor (use-editor.ts:618:3)
    at TemplateConfigSidebar (template-config-sidebar.tsx:36:48)
    at renderWithHooks (react-dom.development.js:11121:18)
    at mountIndeterminateComponent (react-dom.development.js:16869:13)
    at beginWork$1 (react-dom.development.js:18458:16)
    at beginWork (react-dom.development.js:26927:14)
    at performUnitOfWork (react-dom.development.js:25748:12)
    at workLoopSync (react-dom.development.js:25464:5)
    at renderRootSync (react-dom.development.js:25419:7)
    at recoverFromConcurrentError (react-dom.development.js:24597:20)
    at performSyncWorkOnRoot (react-dom.development.js:24899:20)
    at flushSyncWorkAcrossRoots_impl (react-dom.development.js:7758:13)
    at flushSyncWorkOnAllRoots (react-dom.development.js:7718:3)
    at processRootScheduleInMicrotask (react-dom.development.js:7863:3)
    at eval (react-dom.development.js:8034:7)
useEditor @ use-editor.ts:618
TemplateConfigSidebar @ template-config-sidebar.tsx:36
renderWithHooks @ react-dom.development.js:11121
mountIndeterminateComponent @ react-dom.development.js:16869
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
recoverFromConcurrentError @ react-dom.development.js:24597
performSyncWorkOnRoot @ react-dom.development.js:24899
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
redirect-boundary.js:57 Uncaught TypeError: Cannot destructure property 'defaultState' of 'param' as it is undefined.
    at useEditor (use-editor.ts:618:3)
    at TemplateConfigSidebar (template-config-sidebar.tsx:36:48)
    at renderWithHooks (react-dom.development.js:11121:18)
    at mountIndeterminateComponent (react-dom.development.js:16869:13)
    at beginWork$1 (react-dom.development.js:18458:16)
    at beginWork (react-dom.development.js:26927:14)
    at performUnitOfWork (react-dom.development.js:25748:12)
    at workLoopSync (react-dom.development.js:25464:5)
    at renderRootSync (react-dom.development.js:25419:7)
    at recoverFromConcurrentError (react-dom.development.js:24597:20)
    at performSyncWorkOnRoot (react-dom.development.js:24899:20)
    at flushSyncWorkAcrossRoots_impl (react-dom.development.js:7758:13)
    at flushSyncWorkOnAllRoots (react-dom.development.js:7718:3)
    at processRootScheduleInMicrotask (react-dom.development.js:7863:3)
    at eval (react-dom.development.js:8034:7)
useEditor @ use-editor.ts:618
TemplateConfigSidebar @ template-config-sidebar.tsx:36
renderWithHooks @ react-dom.development.js:11121
mountIndeterminateComponent @ react-dom.development.js:16869
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
recoverFromConcurrentError @ react-dom.development.js:24597
performSyncWorkOnRoot @ react-dom.development.js:24899
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
not-found-boundary.js:37 Uncaught TypeError: Cannot destructure property 'defaultState' of 'param' as it is undefined.
    at useEditor (use-editor.ts:618:3)
    at TemplateConfigSidebar (template-config-sidebar.tsx:36:48)
    at renderWithHooks (react-dom.development.js:11121:18)
    at mountIndeterminateComponent (react-dom.development.js:16869:13)
    at beginWork$1 (react-dom.development.js:18458:16)
    at beginWork (react-dom.development.js:26927:14)
    at performUnitOfWork (react-dom.development.js:25748:12)
    at workLoopSync (react-dom.development.js:25464:5)
    at renderRootSync (react-dom.development.js:25419:7)
    at recoverFromConcurrentError (react-dom.development.js:24597:20)
    at performSyncWorkOnRoot (react-dom.development.js:24899:20)
    at flushSyncWorkAcrossRoots_impl (react-dom.development.js:7758:13)
    at flushSyncWorkOnAllRoots (react-dom.development.js:7718:3)
    at processRootScheduleInMicrotask (react-dom.development.js:7863:3)
    at eval (react-dom.development.js:8034:7)
useEditor @ use-editor.ts:618
TemplateConfigSidebar @ template-config-sidebar.tsx:36
renderWithHooks @ react-dom.development.js:11121
mountIndeterminateComponent @ react-dom.development.js:16869
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
recoverFromConcurrentError @ react-dom.development.js:24597
performSyncWorkOnRoot @ react-dom.development.js:24899
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
hook.js:608 The above error occurred in the <NotFoundErrorBoundary> component:

    at TemplateConfigSidebar (webpack-internal:///(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx:37:11)
    at div
    at div
    at Editor (webpack-internal:///(app-pages-browser)/./src/features/editor/components/editor.tsx:64:11)
    at EditorProjectIdPage (webpack-internal:///(app-pages-browser)/./src/app/editor/[projectId]/page.tsx:17:11)
    at ClientPageRoot (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js:14:11)
    at InnerLayoutRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:243:11)
    at RedirectErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:74:9)
    at RedirectBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:82:11)
    at NotFoundBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:84:11)
    at LoadingBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:349:11)
    at ErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:153:9)
    at ScrollAndFocusHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:228:11)
    at RenderFromTemplateContext (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js:16:44)
    at OuterLayoutRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:370:11)
    at InnerLayoutRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:243:11)
    at RedirectErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:74:9)
    at RedirectBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:82:11)
    at NotFoundBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:84:11)
    at LoadingBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:349:11)
    at ErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:153:9)
    at ScrollAndFocusHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:228:11)
    at RenderFromTemplateContext (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js:16:44)
    at OuterLayoutRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:370:11)
    at InnerLayoutRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:243:11)
    at RedirectErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:74:9)
    at RedirectBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:76:9)
    at NotFoundBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:84:11)
    at LoadingBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:349:11)
    at ErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:153:9)
    at ScrollAndFocusHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:228:11)
    at RenderFromTemplateContext (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js:16:44)
    at OuterLayoutRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:370:11)
    at QueryClientProvider (webpack-internal:///(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js:27:11)
    at QueryProvider (webpack-internal:///(app-pages-browser)/./src/components/query-provider.tsx:39:11)
    at Providers (webpack-internal:///(app-pages-browser)/./src/components/providers.tsx:10:11)
    at body
    at html
    at SessionProvider (webpack-internal:///(app-pages-browser)/./node_modules/next-auth/react.js:251:13)
    at RootLayout (Server)
    at RedirectErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:74:9)
    at RedirectBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:76:9)
    at NotFoundBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/ReactDevOverlay.js:87:9)
    at HotReload (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js:321:11)
    at Router (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js:207:11)
    at ErrorBoundaryHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js:113:9)
    at ErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js:160:11)
    at AppRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js:585:13)
    at ServerRoot (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-index.js:112:27)
    at Root (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-index.js:117:11)

React will try to recreate this component tree from scratch using the error boundary you provided, ReactDevOverlay.
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
logCapturedError @ react-dom.development.js:15295
callback @ react-dom.development.js:15357
callCallback @ react-dom.development.js:8696
commitCallbacks @ react-dom.development.js:8743
commitClassCallbacks @ react-dom.development.js:21323
commitLayoutEffectOnFiber @ react-dom.development.js:21425
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21407
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21407
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21418
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21407
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21407
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21407
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21407
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21437
commitLayoutEffects @ react-dom.development.js:22912
commitRootImpl @ react-dom.development.js:26226
commitRoot @ react-dom.development.js:26077
performSyncWorkOnRoot @ react-dom.development.js:24925
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
