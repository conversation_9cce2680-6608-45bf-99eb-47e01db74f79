"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-auth";
exports.ids = ["vendor-chunks/next-auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/next-auth/node_modules/@auth/core/providers/credentials.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/@auth/core/providers/credentials.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Credentials)\n/* harmony export */ });\n/**\n * The Credentials provider allows you to handle signing in with arbitrary credentials,\n * such as a username and password, domain, or two factor authentication or hardware device (e.g. YubiKey U2F / FIDO).\n *\n * It is intended to support use cases where you have an existing system you need to authenticate users against.\n *\n * It comes with the constraint that users authenticated in this manner are not persisted in the database,\n * and consequently that the Credentials provider can only be used if JSON Web Tokens are enabled for sessions.\n *\n * :::caution\n * The functionality provided for credentials-based authentication is intentionally limited to discourage the use of passwords due to the inherent security risks of the username-password model.\n *\n * OAuth providers spend significant amounts of money, time, and engineering effort to build:\n *\n * - abuse detection (bot-protection, rate-limiting)\n * - password management (password reset, credential stuffing, rotation)\n * - data security (encryption/salting, strength validation)\n *\n * and much more for authentication solutions. It is likely that your application would benefit from leveraging these battle-tested solutions rather than try to rebuild them from scratch.\n *\n * If you'd still like to build password-based authentication for your application despite these risks, Auth.js gives you full control to do so.\n *\n * :::\n *\n * See the [callbacks documentation](/reference/core#authconfig#callbacks) for more information on how to interact with the token. For example, you can add additional information to the token by returning an object from the `jwt()` callback:\n *\n * ```js\n * callbacks: {\n *   async jwt({ token, user, account, profile, isNewUser }) {\n *     if (user) {\n *       token.id = user.id\n *     }\n *     return token\n *   }\n * }\n * ```\n *\n * @example\n * ```js\n * import Auth from \"@auth/core\"\n * import Credentials from \"@auth/core/providers/credentials\"\n *\n * const request = new Request(\"https://example.com\")\n * const response = await AuthHandler(request, {\n *   providers: [\n *     Credentials({\n *       credentials: {\n *         username: { label: \"Username\" },\n *         password: {  label: \"Password\", type: \"password\" }\n *       },\n *       async authorize({ request }) {\n *         const response = await fetch(request)\n *         if(!response.ok) return null\n *         return await response.json() ?? null\n *       }\n *     })\n *   ],\n *   secret: \"...\",\n *   trustHost: true,\n * })\n * ```\n * @see [Username/Password Example](https://authjs.dev/getting-started/authentication/credentials)\n */\nfunction Credentials(config) {\n    return {\n        id: \"credentials\",\n        name: \"Credentials\",\n        type: \"credentials\",\n        credentials: {},\n        authorize: () => null,\n        // @ts-expect-error\n        options: config,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/@auth/core/providers/credentials.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/@auth/core/providers/github.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/@auth/core/providers/github.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GitHub)\n/* harmony export */ });\n/**\n * <div style={{backgroundColor: \"#24292f\", display: \"flex\", justifyContent: \"space-between\", color: \"#fff\", padding: 16}}>\n * <span>Built-in <b>GitHub</b> integration.</span>\n * <a href=\"https://github.com\">\n *   <img style={{display: \"block\"}} src=\"https://authjs.dev/img/providers/github.svg\" height=\"48\" width=\"48\"/>\n * </a>\n * </div>\n *\n * @module providers/github\n */\n/**\n * Add GitHub login to your page and make requests to [GitHub APIs](https://docs.github.com/en/rest).\n *\n * ### Setup\n *\n * #### Callback URL\n * ```\n * https://example.com/api/auth/callback/github\n * ```\n *\n * #### Configuration\n * ```ts\n * import { Auth } from \"@auth/core\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * const request = new Request(origin)\n * const response = await Auth(request, {\n *   providers: [GitHub({ clientId: GITHUB_CLIENT_ID, clientSecret: GITHUB_CLIENT_SECRET })],\n * })\n * ```\n *\n * ### Resources\n *\n * - [GitHub - Creating an OAuth App](https://docs.github.com/en/developers/apps/building-oauth-apps/creating-an-oauth-app)\n * - [GitHub - Authorizing OAuth Apps](https://docs.github.com/en/developers/apps/building-oauth-apps/authorizing-oauth-apps)\n * - [GitHub - Configure your GitHub OAuth Apps](https://github.com/settings/developers)\n * - [Learn more about OAuth](https://authjs.dev/concepts/oauth)\n * - [Source code](https://github.com/nextauthjs/next-auth/blob/main/packages/core/src/providers/github.ts)\n *\n * ### Notes\n *\n * By default, Auth.js assumes that the GitHub provider is\n * based on the [OAuth 2](https://www.rfc-editor.org/rfc/rfc6749.html) specification.\n *\n * :::tip\n *\n * The GitHub provider comes with a [default configuration](https://github.com/nextauthjs/next-auth/blob/main/packages/core/src/providers/github.ts).\n * To override the defaults for your use case, check out [customizing a built-in OAuth provider](https://authjs.dev/guides/configuring-oauth-providers).\n *\n * :::\n *\n * :::info **Disclaimer**\n *\n * If you think you found a bug in the default configuration, you can [open an issue](https://authjs.dev/new/provider-issue).\n *\n * Auth.js strictly adheres to the specification and it cannot take responsibility for any deviation from\n * the spec by the provider. You can open an issue, but if the problem is non-compliance with the spec,\n * we might not pursue a resolution. You can ask for more help in [Discussions](https://authjs.dev/new/github-discussions).\n *\n * :::\n */\nfunction GitHub(config) {\n    const baseUrl = config?.enterprise?.baseUrl ?? \"https://github.com\";\n    const apiBaseUrl = config?.enterprise?.baseUrl\n        ? `${config?.enterprise?.baseUrl}/api/v3`\n        : \"https://api.github.com\";\n    return {\n        id: \"github\",\n        name: \"GitHub\",\n        type: \"oauth\",\n        authorization: {\n            url: `${baseUrl}/login/oauth/authorize`,\n            params: { scope: \"read:user user:email\" },\n        },\n        token: `${baseUrl}/login/oauth/access_token`,\n        userinfo: {\n            url: `${apiBaseUrl}/user`,\n            async request({ tokens, provider }) {\n                const profile = await fetch(provider.userinfo?.url, {\n                    headers: {\n                        Authorization: `Bearer ${tokens.access_token}`,\n                        \"User-Agent\": \"authjs\",\n                    },\n                }).then(async (res) => await res.json());\n                if (!profile.email) {\n                    // If the user does not have a public email, get another via the GitHub API\n                    // See https://docs.github.com/en/rest/users/emails#list-public-email-addresses-for-the-authenticated-user\n                    const res = await fetch(`${apiBaseUrl}/user/emails`, {\n                        headers: {\n                            Authorization: `Bearer ${tokens.access_token}`,\n                            \"User-Agent\": \"authjs\",\n                        },\n                    });\n                    if (res.ok) {\n                        const emails = await res.json();\n                        profile.email = (emails.find((e) => e.primary) ?? emails[0]).email;\n                    }\n                }\n                return profile;\n            },\n        },\n        profile(profile) {\n            return {\n                id: profile.id.toString(),\n                name: profile.name ?? profile.login,\n                email: profile.email,\n                image: profile.avatar_url,\n            };\n        },\n        style: { bg: \"#24292f\", text: \"#fff\" },\n        options: config,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/@auth/core/providers/github.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/@auth/core/providers/google.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/@auth/core/providers/google.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Google)\n/* harmony export */ });\n/**\n * Add Google login to your page.\n *\n * ### Setup\n *\n * #### Callback URL\n * ```\n * https://example.com/api/auth/callback/google\n * ```\n *\n * #### Configuration\n *```js\n * import Auth from \"@auth/core\"\n * import Google from \"@auth/core/providers/google\"\n *\n * const request = new Request(origin)\n * const response = await Auth(request, {\n *   providers: [Google({ clientId: GOOGLE_CLIENT_ID, clientSecret: GOOGLE_CLIENT_SECRET })],\n * })\n * ```\n *\n * ### Resources\n *\n *  - [Google OAuth documentation](https://developers.google.com/identity/protocols/oauth2)\n *  - [Google OAuth Configuration](https://console.developers.google.com/apis/credentials)\n *\n * ### Notes\n *\n * By default, Auth.js assumes that the Google provider is\n * based on the [Open ID Connect](https://openid.net/specs/openid-connect-core-1_0.html) specification.\n *\n *\n * The \"Authorized redirect URIs\" used when creating the credentials must include your full domain and end in the callback path. For example;\n *\n * - For production: `https://{YOUR_DOMAIN}/api/auth/callback/google`\n * - For development: `http://localhost:3000/api/auth/callback/google`\n *\n * :::warning\n * Google only provides Refresh Token to an application the first time a user signs in.\n *\n * To force Google to re-issue a Refresh Token, the user needs to remove the application from their account and sign in again:\n * https://myaccount.google.com/permissions\n *\n * Alternatively, you can also pass options in the `params` object of `authorization` which will force the Refresh Token to always be provided on sign in, however this will ask all users to confirm if they wish to grant your application access every time they sign in.\n *\n * If you need access to the RefreshToken or AccessToken for a Google account and you are not using a database to persist user accounts, this may be something you need to do.\n *\n * ```js title=\"pages/api/auth/[...nextauth].js\"\n * const options = {\n *   providers: [\n *     GoogleProvider({\n *       clientId: process.env.GOOGLE_ID,\n *       clientSecret: process.env.GOOGLE_SECRET,\n *       authorization: {\n *         params: {\n *           prompt: \"consent\",\n *           access_type: \"offline\",\n *           response_type: \"code\"\n *         }\n *       }\n *     })\n *   ],\n * }\n * ```\n *\n * :::\n *\n * :::tip\n * Google also returns a `email_verified` boolean property in the OAuth profile.\n *\n * You can use this property to restrict access to people with verified accounts at a particular domain.\n *\n * ```js\n * const options = {\n *   ...\n *   callbacks: {\n *     async signIn({ account, profile }) {\n *       if (account.provider === \"google\") {\n *         return profile.email_verified && profile.email.endsWith(\"@example.com\")\n *       }\n *       return true // Do different verification for other providers that don't have `email_verified`\n *     },\n *   }\n *   ...\n * }\n * ```\n *\n * :::\n * :::tip\n *\n * The Google provider comes with a [default configuration](https://github.com/nextauthjs/next-auth/blob/main/packages/core/src/providers/google.ts).\n * To override the defaults for your use case, check out [customizing a built-in OAuth provider](https://authjs.dev/guides/configuring-oauth-providers).\n *\n * :::\n *\n * :::info **Disclaimer**\n *\n * If you think you found a bug in the default configuration, you can [open an issue](https://authjs.dev/new/provider-issue).\n *\n * Auth.js strictly adheres to the specification and it cannot take responsibility for any deviation from\n * the spec by the provider. You can open an issue, but if the problem is non-compliance with the spec,\n * we might not pursue a resolution. You can ask for more help in [Discussions](https://authjs.dev/new/github-discussions).\n *\n * :::\n */\nfunction Google(options) {\n    return {\n        id: \"google\",\n        name: \"Google\",\n        type: \"oidc\",\n        issuer: \"https://accounts.google.com\",\n        style: {\n            brandColor: \"#1a73e8\",\n        },\n        options,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/@auth/core/providers/google.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/providers/credentials.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/providers/credentials.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _auth_core_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _auth_core_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core/providers/credentials */ \"(rsc)/./node_modules/next-auth/node_modules/@auth/core/providers/credentials.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9jcmVkZW50aWFscy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRDtBQUNVIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9uZXh0LWF1dGgvcHJvdmlkZXJzL2NyZWRlbnRpYWxzLmpzPzczNmMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIkBhdXRoL2NvcmUvcHJvdmlkZXJzL2NyZWRlbnRpYWxzXCI7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIkBhdXRoL2NvcmUvcHJvdmlkZXJzL2NyZWRlbnRpYWxzXCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/providers/credentials.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/providers/github.js":
/*!****************************************************!*\
  !*** ./node_modules/next-auth/providers/github.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _auth_core_providers_github__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _auth_core_providers_github__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core/providers/github */ \"(rsc)/./node_modules/next-auth/node_modules/@auth/core/providers/github.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9naXRodWIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNEM7QUFDVSIsInNvdXJjZXMiOlsid2VicGFjazovL3RoZS1jYW52YXMvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9naXRodWIuanM/OTE5OCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiQGF1dGgvY29yZS9wcm92aWRlcnMvZ2l0aHViXCI7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIkBhdXRoL2NvcmUvcHJvdmlkZXJzL2dpdGh1YlwiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/providers/github.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/providers/google.js":
/*!****************************************************!*\
  !*** ./node_modules/next-auth/providers/google.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _auth_core_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _auth_core_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core/providers/google */ \"(rsc)/./node_modules/next-auth/node_modules/@auth/core/providers/google.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9nb29nbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNEM7QUFDVSIsInNvdXJjZXMiOlsid2VicGFjazovL3RoZS1jYW52YXMvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9nb29nbGUuanM/Njc4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiQGF1dGgvY29yZS9wcm92aWRlcnMvZ29vZ2xlXCI7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIkBhdXRoL2NvcmUvcHJvdmlkZXJzL2dvb2dsZVwiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/providers/google.js\n");

/***/ })

};
;