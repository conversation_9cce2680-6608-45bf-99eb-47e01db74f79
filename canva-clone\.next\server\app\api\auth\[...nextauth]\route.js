"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("perf_hooks");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CLENOVO%5CDownloads%5Cf%5Ccanva-clone%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CLENOVO%5CDownloads%5Cf%5Ccanva-clone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CLENOVO%5CDownloads%5Cf%5Ccanva-clone%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CLENOVO%5CDownloads%5Cf%5Ccanva-clone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_LENOVO_Downloads_f_canva_clone_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_LENOVO_Downloads_f_canva_clone_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/[...nextauth]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CLENOVO%5CDownloads%5Cf%5Ccanva-clone%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CLENOVO%5CDownloads%5Cf%5Ccanva-clone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/auth */ \"(rsc)/./src/auth.ts\");\n\nconst { GET, POST } = _auth__WEBPACK_IMPORTED_MODULE_0__.handlers;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtDO0FBRTNCLE1BQU0sRUFBRUMsR0FBRyxFQUFFQyxJQUFJLEVBQUUsR0FBR0YsMkNBQVFBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vc3JjL2FwcC9hcGkvYXV0aC9bLi4ubmV4dGF1dGhdL3JvdXRlLnRzPzAwOTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaGFuZGxlcnMgfSBmcm9tIFwiQC9hdXRoXCI7XHJcblxyXG5leHBvcnQgY29uc3QgeyBHRVQsIFBPU1QgfSA9IGhhbmRsZXJzO1xyXG4iXSwibmFtZXMiOlsiaGFuZGxlcnMiLCJHRVQiLCJQT1NUIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/auth.config.ts":
/*!****************************!*\
  !*** ./src/auth.config.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _auth_drizzle_adapter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @auth/drizzle-adapter */ \"(rsc)/./node_modules/@auth/drizzle-adapter/index.js\");\n/* harmony import */ var _db_drizzle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/db/drizzle */ \"(rsc)/./src/db/drizzle.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/db/schema */ \"(rsc)/./src/db/schema.ts\");\n\n\n\n\n\n\n\n\n\nconst CredentialsSchema = zod__WEBPACK_IMPORTED_MODULE_7__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().email(),\n    password: zod__WEBPACK_IMPORTED_MODULE_7__.z.string()\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    adapter: (0,_auth_drizzle_adapter__WEBPACK_IMPORTED_MODULE_4__.DrizzleAdapter)(_db_drizzle__WEBPACK_IMPORTED_MODULE_5__.db),\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                pasword: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                const validatedFields = CredentialsSchema.safeParse(credentials);\n                if (!validatedFields.success) {\n                    return null;\n                }\n                const { email, password } = validatedFields.data;\n                const query = await _db_drizzle__WEBPACK_IMPORTED_MODULE_5__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_6__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_8__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_6__.users.email, email));\n                const user = query[0];\n                if (!user || !user.password) {\n                    return null;\n                }\n                const passwordsMatch = await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, user.password);\n                if (!passwordsMatch) {\n                    return null;\n                }\n                return user;\n            }\n        }),\n        next_auth_providers_github__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ],\n    pages: {\n        signIn: \"/sign-in\",\n        error: \"/sign-in\"\n    },\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        session ({ session, token }) {\n            if (token.id) {\n                session.user.id = token.id;\n            }\n            return session;\n        },\n        jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n            }\n            return token;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/auth.config.ts\n");

/***/ }),

/***/ "(rsc)/./src/auth.ts":
/*!*********************!*\
  !*** ./src/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _auth_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/auth.config */ \"(rsc)/./src/auth.config.ts\");\n\n\nconst { handlers, signIn, signOut, auth } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_auth_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXV0aC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBaUM7QUFFTTtBQUVoQyxNQUFNLEVBQUVFLFFBQVEsRUFBRUMsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLElBQUksRUFBRSxHQUFHTCxxREFBUUEsQ0FBQ0Msb0RBQVVBLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vc3JjL2F1dGgudHM/NjJkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTmV4dEF1dGggZnJvbSBcIm5leHQtYXV0aFwiO1xyXG5cclxuaW1wb3J0IGF1dGhDb25maWcgZnJvbSBcIkAvYXV0aC5jb25maWdcIjtcclxuXHJcbmV4cG9ydCBjb25zdCB7IGhhbmRsZXJzLCBzaWduSW4sIHNpZ25PdXQsIGF1dGggfSA9IE5leHRBdXRoKGF1dGhDb25maWcpO1xyXG4iXSwibmFtZXMiOlsiTmV4dEF1dGgiLCJhdXRoQ29uZmlnIiwiaGFuZGxlcnMiLCJzaWduSW4iLCJzaWduT3V0IiwiYXV0aCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/db/drizzle.ts":
/*!***************************!*\
  !*** ./src/db/drizzle.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"(rsc)/./node_modules/postgres/src/index.js\");\n\n\nconst sql = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(process.env.DATABASE_URL);\nconst db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_1__.drizzle)(sql);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvZGIvZHJpenpsZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0Q7QUFDbEI7QUFFaEMsTUFBTUUsTUFBTUQsb0RBQVFBLENBQUNFLFFBQVFDLEdBQUcsQ0FBQ0MsWUFBWTtBQUN0QyxNQUFNQyxLQUFLTixnRUFBT0EsQ0FBQ0UsS0FBSyIsInNvdXJjZXMiOlsid2VicGFjazovL3RoZS1jYW52YXMvLi9zcmMvZGIvZHJpenpsZS50cz84ODA4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRyaXp6bGUgfSBmcm9tIFwiZHJpenpsZS1vcm0vcG9zdGdyZXMtanNcIjtcclxuaW1wb3J0IHBvc3RncmVzIGZyb20gXCJwb3N0Z3Jlc1wiO1xyXG5cclxuY29uc3Qgc3FsID0gcG9zdGdyZXMocHJvY2Vzcy5lbnYuREFUQUJBU0VfVVJMISk7XHJcbmV4cG9ydCBjb25zdCBkYiA9IGRyaXp6bGUoc3FsKTtcclxuIl0sIm5hbWVzIjpbImRyaXp6bGUiLCJwb3N0Z3JlcyIsInNxbCIsInByb2Nlc3MiLCJlbnYiLCJEQVRBQkFTRV9VUkwiLCJkYiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/db/drizzle.ts\n");

/***/ }),

/***/ "(rsc)/./src/db/schema.ts":
/*!**************************!*\
  !*** ./src/db/schema.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   authenticators: () => (/* binding */ authenticators),\n/* harmony export */   projects: () => (/* binding */ projects),\n/* harmony export */   projectsInsertSchema: () => (/* binding */ projectsInsertSchema),\n/* harmony export */   projectsRelations: () => (/* binding */ projectsRelations),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   subscriptions: () => (/* binding */ subscriptions),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   usersRelations: () => (/* binding */ usersRelations),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/relations.js\");\n/* harmony import */ var drizzle_zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! drizzle-zod */ \"(rsc)/./node_modules/drizzle-zod/index.mjs\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/primary-keys.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n\n\n\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"user\", {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"id\").primaryKey().$defaultFn(()=>crypto.randomUUID()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"name\"),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"email\").notNull(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"emailVerified\", {\n        mode: \"date\"\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"image\"),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"password\")\n});\nconst usersRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.relations)(users, ({ many })=>({\n        projects: many(projects)\n    }));\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"account\", {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"userId\").notNull().references(()=>users.id, {\n        onDelete: \"cascade\"\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"type\").$type().notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"provider\").notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"providerAccountId\").notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"refresh_token\"),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"access_token\"),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.integer)(\"expires_at\"),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"token_type\"),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"scope\"),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"id_token\"),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"session_state\")\n}, (account)=>({\n        compoundKey: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.primaryKey)({\n            columns: [\n                account.provider,\n                account.providerAccountId\n            ]\n        })\n    }));\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"session\", {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"sessionToken\").primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"userId\").notNull().references(()=>users.id, {\n        onDelete: \"cascade\"\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"expires\", {\n        mode: \"date\"\n    }).notNull()\n});\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"verificationToken\", {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"identifier\").notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"token\").notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"expires\", {\n        mode: \"date\"\n    }).notNull()\n}, (verificationToken)=>({\n        compositePk: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.primaryKey)({\n            columns: [\n                verificationToken.identifier,\n                verificationToken.token\n            ]\n        })\n    }));\nconst authenticators = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"authenticator\", {\n    credentialID: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"credentialID\").notNull().unique(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"userId\").notNull().references(()=>users.id, {\n        onDelete: \"cascade\"\n    }),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"providerAccountId\").notNull(),\n    credentialPublicKey: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"credentialPublicKey\").notNull(),\n    counter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.integer)(\"counter\").notNull(),\n    credentialDeviceType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"credentialDeviceType\").notNull(),\n    credentialBackedUp: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)(\"credentialBackedUp\").notNull(),\n    transports: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"transports\")\n}, (authenticator)=>({\n        compositePK: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.primaryKey)({\n            columns: [\n                authenticator.userId,\n                authenticator.credentialID\n            ]\n        })\n    }));\nconst projects = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"project\", {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"id\").primaryKey().$defaultFn(()=>crypto.randomUUID()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"name\").notNull(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"userId\").notNull().references(()=>users.id, {\n        onDelete: \"cascade\"\n    }),\n    json: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"json\").notNull(),\n    height: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.integer)(\"height\").notNull(),\n    width: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.integer)(\"width\").notNull(),\n    thumbnailUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"thumbnailUrl\"),\n    isTemplate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)(\"isTemplate\"),\n    isPro: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)(\"isPro\"),\n    isPublic: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)(\"isPublic\").default(false),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"createdAt\", {\n        mode: \"date\"\n    }).notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"updatedAt\", {\n        mode: \"date\"\n    }).notNull()\n});\nconst projectsRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.relations)(projects, ({ one })=>({\n        user: one(users, {\n            fields: [\n                projects.userId\n            ],\n            references: [\n                users.id\n            ]\n        })\n    }));\nconst projectsInsertSchema = (0,drizzle_zod__WEBPACK_IMPORTED_MODULE_0__.createInsertSchema)(projects);\nconst subscriptions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"subscription\", {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"id\").primaryKey().$defaultFn(()=>crypto.randomUUID()),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"userId\").notNull().references(()=>users.id, {\n        onDelete: \"cascade\"\n    }),\n    subscriptionId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"subscriptionId\").notNull(),\n    customerId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"customerId\").notNull(),\n    priceId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"priceId\").notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"status\").notNull(),\n    currentPeriodEnd: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"currentPeriodEnd\", {\n        mode: \"date\"\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"createdAt\", {\n        mode: \"date\"\n    }).notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"updatedAt\", {\n        mode: \"date\"\n    }).notNull()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/db/schema.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/drizzle-orm","vendor-chunks/next-auth","vendor-chunks/@auth","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/oauth4webapi","vendor-chunks/postgres","vendor-chunks/bcryptjs","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/@panva","vendor-chunks/drizzle-zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CLENOVO%5CDownloads%5Cf%5Ccanva-clone%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CLENOVO%5CDownloads%5Cf%5Ccanva-clone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();