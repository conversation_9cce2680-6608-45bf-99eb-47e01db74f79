"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx":
/*!********************************************************************!*\
  !*** ./src/features/editor/components/template-config-sidebar.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateConfigSidebar: function() { return /* binding */ TemplateConfigSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Image,Save,Settings,Trash2,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/projects/api/use-update-template-config */ \"(app-pages-browser)/./src/features/projects/api/use-update-template-config.ts\");\n/* __next_internal_client_entry_do_not_use__ TemplateConfigSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst TemplateConfigSidebar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool, projectId, initialData } = param;\n    _s();\n    const [editableLayers, setEditableLayers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCustomizable, setIsCustomizable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateTemplateConfig = (0,_features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__.useUpdateTemplateConfig)(projectId || \"\");\n    const onClose = ()=>{\n        onChangeActiveTool(\"select\");\n    };\n    const canvas = editor === null || editor === void 0 ? void 0 : editor.canvas;\n    const selectedObjects = (editor === null || editor === void 0 ? void 0 : editor.selectedObjects) || [];\n    // Load existing template configuration from database\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialData) {\n            setIsCustomizable(initialData.isCustomizable || false);\n            if (initialData.editableLayers) {\n                try {\n                    const parsedLayers = JSON.parse(initialData.editableLayers);\n                    setEditableLayers(parsedLayers);\n                    // Apply editable properties to canvas objects\n                    if (canvas) {\n                        parsedLayers.forEach((layer)=>{\n                            const canvasObject = canvas.getObjects().find((obj)=>obj.id === layer.id);\n                            if (canvasObject) {\n                                canvasObject.isEditable = true;\n                                canvasObject.editableType = layer.type;\n                                canvasObject.editableName = layer.name;\n                                canvasObject.editablePlaceholder = layer.placeholder;\n                                canvasObject.editableConstraints = layer.constraints;\n                            }\n                        });\n                        canvas.renderAll();\n                    }\n                } catch (error) {\n                    console.error(\"Failed to parse editable layers:\", error);\n                }\n            }\n        }\n    }, [\n        initialData,\n        canvas\n    ]);\n    // Load existing editable layers from canvas objects (fallback)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!canvas || editableLayers.length > 0) return;\n        const layers = [];\n        canvas.getObjects().forEach((obj)=>{\n            const editableObj = obj;\n            if (editableObj.isEditable) {\n                layers.push({\n                    id: editableObj.id || \"\",\n                    type: editableObj.editableType || \"text\",\n                    name: editableObj.editableName || \"Unnamed Layer\",\n                    originalValue: editableObj.type === \"textbox\" ? editableObj.text : \"\",\n                    placeholder: editableObj.editablePlaceholder,\n                    constraints: editableObj.editableConstraints\n                });\n            }\n        });\n        if (layers.length > 0) {\n            setEditableLayers(layers);\n            setIsCustomizable(true);\n        }\n    }, [\n        canvas,\n        editableLayers.length\n    ]);\n    const makeLayerEditable = (type)=>{\n        if (!canvas || selectedObjects.length === 0) return;\n        const selectedObject = selectedObjects[0];\n        // Validate object type\n        if (type === \"text\" && selectedObject.type !== \"textbox\") {\n            alert(\"Please select a text object to make it editable\");\n            return;\n        }\n        if (type === \"image\" && ![\n            \"image\",\n            \"rect\",\n            \"circle\"\n        ].includes(selectedObject.type || \"\")) {\n            alert(\"Please select an image or shape to make it editable\");\n            return;\n        }\n        // Mark object as editable\n        selectedObject.isEditable = true;\n        selectedObject.editableType = type;\n        selectedObject.editableName = \"\".concat(type === \"text\" ? \"Text\" : \"Image\", \" \").concat(editableLayers.length + 1);\n        if (type === \"text\") {\n            selectedObject.editablePlaceholder = \"Enter your text here...\";\n        }\n        // Add to editable layers list\n        const newLayer = {\n            id: selectedObject.id || \"layer_\".concat(Date.now()),\n            type,\n            name: selectedObject.editableName,\n            originalValue: type === \"text\" ? selectedObject.text : \"\",\n            placeholder: selectedObject.editablePlaceholder,\n            constraints: {\n                maxLength: type === \"text\" ? 100 : undefined,\n                allowedFormats: type === \"image\" ? [\n                    \"jpg\",\n                    \"jpeg\",\n                    \"png\",\n                    \"gif\"\n                ] : undefined,\n                maxFileSize: type === \"image\" ? 5 * 1024 * 1024 : undefined\n            }\n        };\n        // Assign ID if not exists\n        if (!selectedObject.id) {\n            selectedObject.id = newLayer.id;\n        }\n        setEditableLayers([\n            ...editableLayers,\n            newLayer\n        ]);\n        setIsCustomizable(true);\n        canvas.renderAll();\n    };\n    const removeEditableLayer = (layerId)=>{\n        // Find and update the canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.isEditable = false;\n            delete canvasObject.editableType;\n            delete canvasObject.editableName;\n            delete canvasObject.editablePlaceholder;\n            delete canvasObject.editableConstraints;\n        }\n        // Remove from layers list\n        const updatedLayers = editableLayers.filter((layer)=>layer.id !== layerId);\n        setEditableLayers(updatedLayers);\n        setIsCustomizable(updatedLayers.length > 0);\n        canvas === null || canvas === void 0 ? void 0 : canvas.renderAll();\n    };\n    const updateLayerName = (layerId, name)=>{\n        const updatedLayers = editableLayers.map((layer)=>layer.id === layerId ? {\n                ...layer,\n                name\n            } : layer);\n        setEditableLayers(updatedLayers);\n        // Update canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.editableName = name;\n        }\n    };\n    const updateLayerPlaceholder = (layerId, placeholder)=>{\n        const updatedLayers = editableLayers.map((layer)=>layer.id === layerId ? {\n                ...layer,\n                placeholder\n            } : layer);\n        setEditableLayers(updatedLayers);\n        // Update canvas object\n        const canvasObject = canvas === null || canvas === void 0 ? void 0 : canvas.getObjects().find((obj)=>obj.id === layerId);\n        if (canvasObject) {\n            canvasObject.editablePlaceholder = placeholder;\n        }\n    };\n    const saveTemplateConfig = ()=>{\n        if (!projectId) {\n            alert(\"Project ID is required to save template configuration\");\n            return;\n        }\n        updateTemplateConfig.mutate({\n            isCustomizable,\n            editableLayers: JSON.stringify(editableLayers)\n        });\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"bg-white relative border-r z-[40] w-[360px] h-full flex flex-col\", activeTool === \"template-config\" ? \"visible\" : \"hidden\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: \"Template Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onClose,\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 mt-1\",\n                        children: \"Configure which elements users can customize\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__.ScrollArea, {\n                className: \"flex-1 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Template Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"customizable\",\n                                                className: \"text-sm\",\n                                                children: \"Make Customizable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_5__.Switch, {\n                                                id: \"customizable\",\n                                                checked: isCustomizable,\n                                                onCheckedChange: setIsCustomizable\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-2\",\n                                        children: \"Allow public users to customize this template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm\",\n                                    children: \"Add Editable Elements\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full justify-start\",\n                                        onClick: ()=>makeLayerEditable(\"text\"),\n                                        disabled: selectedObjects.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make Text Editable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full justify-start\",\n                                        onClick: ()=>makeLayerEditable(\"image\"),\n                                        disabled: selectedObjects.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Make Image Replaceable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    selectedObjects.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"Select an element on the canvas first\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, undefined),\n                    editableLayers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-sm\",\n                                    children: [\n                                        \"Editable Elements (\",\n                                        editableLayers.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-3\",\n                                children: editableLayers.map((layer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                        variant: layer.type === \"text\" ? \"default\" : \"secondary\",\n                                                        children: [\n                                                            layer.type === \"text\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 25\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            layer.type\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>removeEditableLayer(layer.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-xs\",\n                                                                children: \"Display Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: layer.name,\n                                                                onChange: (e)=>updateLayerName(layer.id, e.target.value),\n                                                                className: \"h-8 text-sm\",\n                                                                placeholder: \"e.g., Your Name, Profile Photo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    layer.type === \"text\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-xs\",\n                                                                children: \"Placeholder Text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: layer.placeholder || \"\",\n                                                                onChange: (e)=>updateLayerPlaceholder(layer.id, e.target.value),\n                                                                className: \"h-8 text-sm\",\n                                                                placeholder: \"Enter placeholder text...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, layer.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: saveTemplateConfig,\n                    className: \"w-full\",\n                    disabled: !isCustomizable || editableLayers.length === 0,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Image_Save_Settings_Trash2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"h-4 w-4 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, undefined),\n                        \"Save Template Config\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\f\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\template-config-sidebar.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateConfigSidebar, \"NtA008s/TQlf6xbBqh+lddVhxNc=\", false, function() {\n    return [\n        _features_projects_api_use_update_template_config__WEBPACK_IMPORTED_MODULE_10__.useUpdateTemplateConfig\n    ];\n});\n_c = TemplateConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TemplateConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/template-config-sidebar.tsx\n"));

/***/ })

});