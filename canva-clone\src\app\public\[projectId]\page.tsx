"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Loader2, <PERSON>Left, Eye, User, Calendar } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import Image from "next/image";

import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface PublicProject {
  id: string;
  name: string;
  width: number;
  height: number;
  thumbnailUrl: string | null;
  json: string;
  createdAt: string;
  updatedAt: string;
  user: {
    name: string | null;
    image: string | null;
  } | null;
}

export default function PublicProjectViewPage() {
  const params = useParams();
  const router = useRouter();
  const [project, setProject] = useState<PublicProject | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProject = async () => {
      try {
        const response = await fetch(`/api/projects/public/${params.projectId}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            setError("Project not found or not public");
          } else {
            setError("Failed to load project");
          }
          return;
        }
        
        const data = await response.json();
        setProject(data.data);
      } catch (err) {
        setError("Failed to load project");
      } finally {
        setLoading(false);
      }
    };

    if (params.projectId) {
      fetchProject();
    }
  }, [params.projectId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {error || "Project not found"}
            </h1>
            <Button onClick={() => router.push("/public")} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Gallery
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                onClick={() => router.push("/public")} 
                variant="ghost" 
                size="sm"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Gallery
              </Button>
              <div className="h-6 w-px bg-gray-300" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">{project.name}</h1>
                <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                  <Badge variant="secondary">
                    {project.width} × {project.height}
                  </Badge>
                  <span className="flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    {formatDistanceToNow(new Date(project.updatedAt), { addSuffix: true })}
                  </span>
                  {project.user && (
                    <span className="flex items-center">
                      <User className="h-3 w-3 mr-1" />
                      {project.user.name || "Anonymous"}
                    </span>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-green-600 border-green-200">
                <Eye className="h-3 w-3 mr-1" />
                Public
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
          {/* Project Preview */}
          <div className="flex items-center justify-center p-8 bg-gray-50">
            <div 
              className="bg-white rounded-lg shadow-lg overflow-hidden"
              style={{ 
                aspectRatio: `${project.width}/${project.height}`,
                maxWidth: "800px",
                width: "100%"
              }}
            >
              {project.thumbnailUrl ? (
                <Image
                  src={project.thumbnailUrl}
                  alt={project.name}
                  width={project.width}
                  height={project.height}
                  className="w-full h-full object-contain"
                  quality={100}
                />
              ) : (
                <div className="flex h-full w-full items-center justify-center bg-gray-100">
                  <Eye className="h-12 w-12 text-gray-400" />
                </div>
              )}
            </div>
          </div>

          {/* Project Info */}
          <div className="p-6 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Project Details</h3>
                <dl className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <dt className="text-gray-500">Dimensions:</dt>
                    <dd className="text-gray-900">{project.width} × {project.height} px</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-gray-500">Created:</dt>
                    <dd className="text-gray-900">
                      {formatDistanceToNow(new Date(project.createdAt), { addSuffix: true })}
                    </dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-gray-500">Last updated:</dt>
                    <dd className="text-gray-900">
                      {formatDistanceToNow(new Date(project.updatedAt), { addSuffix: true })}
                    </dd>
                  </div>
                </dl>
              </div>
              
              {project.user && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Creator</h3>
                  <div className="flex items-center space-x-3">
                    {project.user.image ? (
                      <Image
                        src={project.user.image}
                        alt={project.user.name || "User"}
                        width={40}
                        height={40}
                        className="rounded-full"
                      />
                    ) : (
                      <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-gray-600" />
                      </div>
                    )}
                    <div>
                      <p className="font-medium text-gray-900">
                        {project.user.name || "Anonymous"}
                      </p>
                      <p className="text-sm text-gray-500">Designer</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
