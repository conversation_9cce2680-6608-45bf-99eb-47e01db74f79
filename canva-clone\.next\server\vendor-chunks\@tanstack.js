"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/focusManager.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/focusManager.ts\n\n\nvar FocusManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #focused;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onFocus) => {\n      if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const listener = () => onFocus();\n        window.addEventListener(\"visibilitychange\", listener, false);\n        return () => {\n          window.removeEventListener(\"visibilitychange\", listener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === \"boolean\") {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n  setFocused(focused) {\n    const changed = this.#focused !== focused;\n    if (changed) {\n      this.#focused = focused;\n      this.onFocus();\n    }\n  }\n  onFocus() {\n    const isFocused = this.isFocused();\n    this.listeners.forEach((listener) => {\n      listener(isFocused);\n    });\n  }\n  isFocused() {\n    if (typeof this.#focused === \"boolean\") {\n      return this.#focused;\n    }\n    return globalThis.document?.visibilityState !== \"hidden\";\n  }\n};\nvar focusManager = new FocusManager();\n\n//# sourceMappingURL=focusManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),\n/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),\n/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/infiniteQueryBehavior.ts\n\nfunction infiniteQueryBehavior(pages) {\n  return {\n    onFetch: (context, query) => {\n      const fetchFn = async () => {\n        const options = context.options;\n        const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n        const oldPages = context.state.data?.pages || [];\n        const oldPageParams = context.state.data?.pageParams || [];\n        const empty = { pages: [], pageParams: [] };\n        let cancelled = false;\n        const addSignalProperty = (object) => {\n          Object.defineProperty(object, \"signal\", {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true;\n              } else {\n                context.signal.addEventListener(\"abort\", () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        };\n        const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureQueryFn)(context.options, context.fetchOptions);\n        const fetchPage = async (data, param, previous) => {\n          if (cancelled) {\n            return Promise.reject();\n          }\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data);\n          }\n          const queryFnContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            direction: previous ? \"backward\" : \"forward\",\n            meta: context.options.meta\n          };\n          addSignalProperty(queryFnContext);\n          const page = await queryFn(\n            queryFnContext\n          );\n          const { maxPages } = context.options;\n          const addTo = previous ? _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToStart : _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToEnd;\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          };\n        };\n        let result;\n        if (direction && oldPages.length) {\n          const previous = direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams\n          };\n          const param = pageParamFn(options, oldData);\n          result = await fetchPage(oldData, param, previous);\n        } else {\n          result = await fetchPage(\n            empty,\n            oldPageParams[0] ?? options.initialPageParam\n          );\n          const remainingPages = pages ?? oldPages.length;\n          for (let i = 1; i < remainingPages; i++) {\n            const param = getNextPageParam(options, result);\n            result = await fetchPage(result, param);\n          }\n        }\n        return result;\n      };\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn,\n            {\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal\n            },\n            query\n          );\n        };\n      } else {\n        context.fetchFn = fetchFn;\n      }\n    }\n  };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n  const lastIndex = pages.length - 1;\n  return options.getNextPageParam(\n    pages[lastIndex],\n    pages,\n    pageParams[lastIndex],\n    pageParams\n  );\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n  return options.getPreviousPageParam?.(\n    pages[0],\n    pages,\n    pageParams[0],\n    pageParams\n  );\n}\nfunction hasNextPage(options, data) {\n  if (!data)\n    return false;\n  return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n  if (!data || !options.getPreviousPageParam)\n    return false;\n  return getPreviousPageParam(options, data) != null;\n}\n\n//# sourceMappingURL=infiniteQueryBehavior.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL2luZmluaXRlUXVlcnlCZWhhdmlvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDaUU7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBLHdCQUF3Qix3REFBYTtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLFdBQVc7QUFDN0IsbUNBQW1DLGlEQUFVLEdBQUcsK0NBQVE7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsb0JBQW9CO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsbUJBQW1CO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5Q0FBeUMsbUJBQW1CO0FBQzVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFLRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcXVlcnktY29yZS9idWlsZC9tb2Rlcm4vaW5maW5pdGVRdWVyeUJlaGF2aW9yLmpzP2M1ODIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2luZmluaXRlUXVlcnlCZWhhdmlvci50c1xuaW1wb3J0IHsgYWRkVG9FbmQsIGFkZFRvU3RhcnQsIGVuc3VyZVF1ZXJ5Rm4gfSBmcm9tIFwiLi91dGlscy5qc1wiO1xuZnVuY3Rpb24gaW5maW5pdGVRdWVyeUJlaGF2aW9yKHBhZ2VzKSB7XG4gIHJldHVybiB7XG4gICAgb25GZXRjaDogKGNvbnRleHQsIHF1ZXJ5KSA9PiB7XG4gICAgICBjb25zdCBmZXRjaEZuID0gYXN5bmMgKCkgPT4ge1xuICAgICAgICBjb25zdCBvcHRpb25zID0gY29udGV4dC5vcHRpb25zO1xuICAgICAgICBjb25zdCBkaXJlY3Rpb24gPSBjb250ZXh0LmZldGNoT3B0aW9ucz8ubWV0YT8uZmV0Y2hNb3JlPy5kaXJlY3Rpb247XG4gICAgICAgIGNvbnN0IG9sZFBhZ2VzID0gY29udGV4dC5zdGF0ZS5kYXRhPy5wYWdlcyB8fCBbXTtcbiAgICAgICAgY29uc3Qgb2xkUGFnZVBhcmFtcyA9IGNvbnRleHQuc3RhdGUuZGF0YT8ucGFnZVBhcmFtcyB8fCBbXTtcbiAgICAgICAgY29uc3QgZW1wdHkgPSB7IHBhZ2VzOiBbXSwgcGFnZVBhcmFtczogW10gfTtcbiAgICAgICAgbGV0IGNhbmNlbGxlZCA9IGZhbHNlO1xuICAgICAgICBjb25zdCBhZGRTaWduYWxQcm9wZXJ0eSA9IChvYmplY3QpID0+IHtcbiAgICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkob2JqZWN0LCBcInNpZ25hbFwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgZ2V0OiAoKSA9PiB7XG4gICAgICAgICAgICAgIGlmIChjb250ZXh0LnNpZ25hbC5hYm9ydGVkKSB7XG4gICAgICAgICAgICAgICAgY2FuY2VsbGVkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBjb250ZXh0LnNpZ25hbC5hZGRFdmVudExpc3RlbmVyKFwiYWJvcnRcIiwgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgY2FuY2VsbGVkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICByZXR1cm4gY29udGV4dC5zaWduYWw7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IHF1ZXJ5Rm4gPSBlbnN1cmVRdWVyeUZuKGNvbnRleHQub3B0aW9ucywgY29udGV4dC5mZXRjaE9wdGlvbnMpO1xuICAgICAgICBjb25zdCBmZXRjaFBhZ2UgPSBhc3luYyAoZGF0YSwgcGFyYW0sIHByZXZpb3VzKSA9PiB7XG4gICAgICAgICAgaWYgKGNhbmNlbGxlZCkge1xuICAgICAgICAgICAgcmV0dXJuIFByb21pc2UucmVqZWN0KCk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGlmIChwYXJhbSA9PSBudWxsICYmIGRhdGEucGFnZXMubGVuZ3RoKSB7XG4gICAgICAgICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKGRhdGEpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBjb25zdCBxdWVyeUZuQ29udGV4dCA9IHtcbiAgICAgICAgICAgIHF1ZXJ5S2V5OiBjb250ZXh0LnF1ZXJ5S2V5LFxuICAgICAgICAgICAgcGFnZVBhcmFtOiBwYXJhbSxcbiAgICAgICAgICAgIGRpcmVjdGlvbjogcHJldmlvdXMgPyBcImJhY2t3YXJkXCIgOiBcImZvcndhcmRcIixcbiAgICAgICAgICAgIG1ldGE6IGNvbnRleHQub3B0aW9ucy5tZXRhXG4gICAgICAgICAgfTtcbiAgICAgICAgICBhZGRTaWduYWxQcm9wZXJ0eShxdWVyeUZuQ29udGV4dCk7XG4gICAgICAgICAgY29uc3QgcGFnZSA9IGF3YWl0IHF1ZXJ5Rm4oXG4gICAgICAgICAgICBxdWVyeUZuQ29udGV4dFxuICAgICAgICAgICk7XG4gICAgICAgICAgY29uc3QgeyBtYXhQYWdlcyB9ID0gY29udGV4dC5vcHRpb25zO1xuICAgICAgICAgIGNvbnN0IGFkZFRvID0gcHJldmlvdXMgPyBhZGRUb1N0YXJ0IDogYWRkVG9FbmQ7XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHBhZ2VzOiBhZGRUbyhkYXRhLnBhZ2VzLCBwYWdlLCBtYXhQYWdlcyksXG4gICAgICAgICAgICBwYWdlUGFyYW1zOiBhZGRUbyhkYXRhLnBhZ2VQYXJhbXMsIHBhcmFtLCBtYXhQYWdlcylcbiAgICAgICAgICB9O1xuICAgICAgICB9O1xuICAgICAgICBsZXQgcmVzdWx0O1xuICAgICAgICBpZiAoZGlyZWN0aW9uICYmIG9sZFBhZ2VzLmxlbmd0aCkge1xuICAgICAgICAgIGNvbnN0IHByZXZpb3VzID0gZGlyZWN0aW9uID09PSBcImJhY2t3YXJkXCI7XG4gICAgICAgICAgY29uc3QgcGFnZVBhcmFtRm4gPSBwcmV2aW91cyA/IGdldFByZXZpb3VzUGFnZVBhcmFtIDogZ2V0TmV4dFBhZ2VQYXJhbTtcbiAgICAgICAgICBjb25zdCBvbGREYXRhID0ge1xuICAgICAgICAgICAgcGFnZXM6IG9sZFBhZ2VzLFxuICAgICAgICAgICAgcGFnZVBhcmFtczogb2xkUGFnZVBhcmFtc1xuICAgICAgICAgIH07XG4gICAgICAgICAgY29uc3QgcGFyYW0gPSBwYWdlUGFyYW1GbihvcHRpb25zLCBvbGREYXRhKTtcbiAgICAgICAgICByZXN1bHQgPSBhd2FpdCBmZXRjaFBhZ2Uob2xkRGF0YSwgcGFyYW0sIHByZXZpb3VzKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZXN1bHQgPSBhd2FpdCBmZXRjaFBhZ2UoXG4gICAgICAgICAgICBlbXB0eSxcbiAgICAgICAgICAgIG9sZFBhZ2VQYXJhbXNbMF0gPz8gb3B0aW9ucy5pbml0aWFsUGFnZVBhcmFtXG4gICAgICAgICAgKTtcbiAgICAgICAgICBjb25zdCByZW1haW5pbmdQYWdlcyA9IHBhZ2VzID8/IG9sZFBhZ2VzLmxlbmd0aDtcbiAgICAgICAgICBmb3IgKGxldCBpID0gMTsgaSA8IHJlbWFpbmluZ1BhZ2VzOyBpKyspIHtcbiAgICAgICAgICAgIGNvbnN0IHBhcmFtID0gZ2V0TmV4dFBhZ2VQYXJhbShvcHRpb25zLCByZXN1bHQpO1xuICAgICAgICAgICAgcmVzdWx0ID0gYXdhaXQgZmV0Y2hQYWdlKHJlc3VsdCwgcGFyYW0pO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgfTtcbiAgICAgIGlmIChjb250ZXh0Lm9wdGlvbnMucGVyc2lzdGVyKSB7XG4gICAgICAgIGNvbnRleHQuZmV0Y2hGbiA9ICgpID0+IHtcbiAgICAgICAgICByZXR1cm4gY29udGV4dC5vcHRpb25zLnBlcnNpc3Rlcj8uKFxuICAgICAgICAgICAgZmV0Y2hGbixcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgcXVlcnlLZXk6IGNvbnRleHQucXVlcnlLZXksXG4gICAgICAgICAgICAgIG1ldGE6IGNvbnRleHQub3B0aW9ucy5tZXRhLFxuICAgICAgICAgICAgICBzaWduYWw6IGNvbnRleHQuc2lnbmFsXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgcXVlcnlcbiAgICAgICAgICApO1xuICAgICAgICB9O1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29udGV4dC5mZXRjaEZuID0gZmV0Y2hGbjtcbiAgICAgIH1cbiAgICB9XG4gIH07XG59XG5mdW5jdGlvbiBnZXROZXh0UGFnZVBhcmFtKG9wdGlvbnMsIHsgcGFnZXMsIHBhZ2VQYXJhbXMgfSkge1xuICBjb25zdCBsYXN0SW5kZXggPSBwYWdlcy5sZW5ndGggLSAxO1xuICByZXR1cm4gb3B0aW9ucy5nZXROZXh0UGFnZVBhcmFtKFxuICAgIHBhZ2VzW2xhc3RJbmRleF0sXG4gICAgcGFnZXMsXG4gICAgcGFnZVBhcmFtc1tsYXN0SW5kZXhdLFxuICAgIHBhZ2VQYXJhbXNcbiAgKTtcbn1cbmZ1bmN0aW9uIGdldFByZXZpb3VzUGFnZVBhcmFtKG9wdGlvbnMsIHsgcGFnZXMsIHBhZ2VQYXJhbXMgfSkge1xuICByZXR1cm4gb3B0aW9ucy5nZXRQcmV2aW91c1BhZ2VQYXJhbT8uKFxuICAgIHBhZ2VzWzBdLFxuICAgIHBhZ2VzLFxuICAgIHBhZ2VQYXJhbXNbMF0sXG4gICAgcGFnZVBhcmFtc1xuICApO1xufVxuZnVuY3Rpb24gaGFzTmV4dFBhZ2Uob3B0aW9ucywgZGF0YSkge1xuICBpZiAoIWRhdGEpXG4gICAgcmV0dXJuIGZhbHNlO1xuICByZXR1cm4gZ2V0TmV4dFBhZ2VQYXJhbShvcHRpb25zLCBkYXRhKSAhPSBudWxsO1xufVxuZnVuY3Rpb24gaGFzUHJldmlvdXNQYWdlKG9wdGlvbnMsIGRhdGEpIHtcbiAgaWYgKCFkYXRhIHx8ICFvcHRpb25zLmdldFByZXZpb3VzUGFnZVBhcmFtKVxuICAgIHJldHVybiBmYWxzZTtcbiAgcmV0dXJuIGdldFByZXZpb3VzUGFnZVBhcmFtKG9wdGlvbnMsIGRhdGEpICE9IG51bGw7XG59XG5leHBvcnQge1xuICBoYXNOZXh0UGFnZSxcbiAgaGFzUHJldmlvdXNQYWdlLFxuICBpbmZpbml0ZVF1ZXJ5QmVoYXZpb3Jcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmZpbml0ZVF1ZXJ5QmVoYXZpb3IuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InfiniteQueryObserver: () => (/* binding */ InfiniteQueryObserver)\n/* harmony export */ });\n/* harmony import */ var _queryObserver_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./queryObserver.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js\");\n/* harmony import */ var _infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./infiniteQueryBehavior.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\");\n// src/infiniteQueryObserver.ts\n\n\nvar InfiniteQueryObserver = class extends _queryObserver_js__WEBPACK_IMPORTED_MODULE_0__.QueryObserver {\n  constructor(client, options) {\n    super(client, options);\n  }\n  bindMethods() {\n    super.bindMethods();\n    this.fetchNextPage = this.fetchNextPage.bind(this);\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n  }\n  setOptions(options, notifyOptions) {\n    super.setOptions(\n      {\n        ...options,\n        behavior: (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_1__.infiniteQueryBehavior)()\n      },\n      notifyOptions\n    );\n  }\n  getOptimisticResult(options) {\n    options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_1__.infiniteQueryBehavior)();\n    return super.getOptimisticResult(options);\n  }\n  fetchNextPage(options) {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: \"forward\" }\n      }\n    });\n  }\n  fetchPreviousPage(options) {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: \"backward\" }\n      }\n    });\n  }\n  createResult(query, options) {\n    const { state } = query;\n    const parentResult = super.createResult(query, options);\n    const { isFetching, isRefetching, isError, isRefetchError } = parentResult;\n    const fetchDirection = state.fetchMeta?.fetchMore?.direction;\n    const isFetchNextPageError = isError && fetchDirection === \"forward\";\n    const isFetchingNextPage = isFetching && fetchDirection === \"forward\";\n    const isFetchPreviousPageError = isError && fetchDirection === \"backward\";\n    const isFetchingPreviousPage = isFetching && fetchDirection === \"backward\";\n    const result = {\n      ...parentResult,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_1__.hasNextPage)(options, state.data),\n      hasPreviousPage: (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_1__.hasPreviousPage)(options, state.data),\n      isFetchNextPageError,\n      isFetchingNextPage,\n      isFetchPreviousPageError,\n      isFetchingPreviousPage,\n      isRefetchError: isRefetchError && !isFetchNextPageError && !isFetchPreviousPageError,\n      isRefetching: isRefetching && !isFetchingNextPage && !isFetchingPreviousPage\n    };\n    return result;\n  }\n};\n\n//# sourceMappingURL=infiniteQueryObserver.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL2luZmluaXRlUXVlcnlPYnNlcnZlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNtRDtBQUtmO0FBQ3BDLDBDQUEwQyw0REFBYTtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsZ0ZBQXFCO0FBQ3ZDLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixnRkFBcUI7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVksUUFBUTtBQUNwQjtBQUNBLFlBQVksb0RBQW9EO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixzRUFBVztBQUM5Qix1QkFBdUIsMEVBQWU7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcXVlcnktY29yZS9idWlsZC9tb2Rlcm4vaW5maW5pdGVRdWVyeU9ic2VydmVyLmpzP2QwNDQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2luZmluaXRlUXVlcnlPYnNlcnZlci50c1xuaW1wb3J0IHsgUXVlcnlPYnNlcnZlciB9IGZyb20gXCIuL3F1ZXJ5T2JzZXJ2ZXIuanNcIjtcbmltcG9ydCB7XG4gIGhhc05leHRQYWdlLFxuICBoYXNQcmV2aW91c1BhZ2UsXG4gIGluZmluaXRlUXVlcnlCZWhhdmlvclxufSBmcm9tIFwiLi9pbmZpbml0ZVF1ZXJ5QmVoYXZpb3IuanNcIjtcbnZhciBJbmZpbml0ZVF1ZXJ5T2JzZXJ2ZXIgPSBjbGFzcyBleHRlbmRzIFF1ZXJ5T2JzZXJ2ZXIge1xuICBjb25zdHJ1Y3RvcihjbGllbnQsIG9wdGlvbnMpIHtcbiAgICBzdXBlcihjbGllbnQsIG9wdGlvbnMpO1xuICB9XG4gIGJpbmRNZXRob2RzKCkge1xuICAgIHN1cGVyLmJpbmRNZXRob2RzKCk7XG4gICAgdGhpcy5mZXRjaE5leHRQYWdlID0gdGhpcy5mZXRjaE5leHRQYWdlLmJpbmQodGhpcyk7XG4gICAgdGhpcy5mZXRjaFByZXZpb3VzUGFnZSA9IHRoaXMuZmV0Y2hQcmV2aW91c1BhZ2UuYmluZCh0aGlzKTtcbiAgfVxuICBzZXRPcHRpb25zKG9wdGlvbnMsIG5vdGlmeU9wdGlvbnMpIHtcbiAgICBzdXBlci5zZXRPcHRpb25zKFxuICAgICAge1xuICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICBiZWhhdmlvcjogaW5maW5pdGVRdWVyeUJlaGF2aW9yKClcbiAgICAgIH0sXG4gICAgICBub3RpZnlPcHRpb25zXG4gICAgKTtcbiAgfVxuICBnZXRPcHRpbWlzdGljUmVzdWx0KG9wdGlvbnMpIHtcbiAgICBvcHRpb25zLmJlaGF2aW9yID0gaW5maW5pdGVRdWVyeUJlaGF2aW9yKCk7XG4gICAgcmV0dXJuIHN1cGVyLmdldE9wdGltaXN0aWNSZXN1bHQob3B0aW9ucyk7XG4gIH1cbiAgZmV0Y2hOZXh0UGFnZShvcHRpb25zKSB7XG4gICAgcmV0dXJuIHRoaXMuZmV0Y2goe1xuICAgICAgLi4ub3B0aW9ucyxcbiAgICAgIG1ldGE6IHtcbiAgICAgICAgZmV0Y2hNb3JlOiB7IGRpcmVjdGlvbjogXCJmb3J3YXJkXCIgfVxuICAgICAgfVxuICAgIH0pO1xuICB9XG4gIGZldGNoUHJldmlvdXNQYWdlKG9wdGlvbnMpIHtcbiAgICByZXR1cm4gdGhpcy5mZXRjaCh7XG4gICAgICAuLi5vcHRpb25zLFxuICAgICAgbWV0YToge1xuICAgICAgICBmZXRjaE1vcmU6IHsgZGlyZWN0aW9uOiBcImJhY2t3YXJkXCIgfVxuICAgICAgfVxuICAgIH0pO1xuICB9XG4gIGNyZWF0ZVJlc3VsdChxdWVyeSwgb3B0aW9ucykge1xuICAgIGNvbnN0IHsgc3RhdGUgfSA9IHF1ZXJ5O1xuICAgIGNvbnN0IHBhcmVudFJlc3VsdCA9IHN1cGVyLmNyZWF0ZVJlc3VsdChxdWVyeSwgb3B0aW9ucyk7XG4gICAgY29uc3QgeyBpc0ZldGNoaW5nLCBpc1JlZmV0Y2hpbmcsIGlzRXJyb3IsIGlzUmVmZXRjaEVycm9yIH0gPSBwYXJlbnRSZXN1bHQ7XG4gICAgY29uc3QgZmV0Y2hEaXJlY3Rpb24gPSBzdGF0ZS5mZXRjaE1ldGE/LmZldGNoTW9yZT8uZGlyZWN0aW9uO1xuICAgIGNvbnN0IGlzRmV0Y2hOZXh0UGFnZUVycm9yID0gaXNFcnJvciAmJiBmZXRjaERpcmVjdGlvbiA9PT0gXCJmb3J3YXJkXCI7XG4gICAgY29uc3QgaXNGZXRjaGluZ05leHRQYWdlID0gaXNGZXRjaGluZyAmJiBmZXRjaERpcmVjdGlvbiA9PT0gXCJmb3J3YXJkXCI7XG4gICAgY29uc3QgaXNGZXRjaFByZXZpb3VzUGFnZUVycm9yID0gaXNFcnJvciAmJiBmZXRjaERpcmVjdGlvbiA9PT0gXCJiYWNrd2FyZFwiO1xuICAgIGNvbnN0IGlzRmV0Y2hpbmdQcmV2aW91c1BhZ2UgPSBpc0ZldGNoaW5nICYmIGZldGNoRGlyZWN0aW9uID09PSBcImJhY2t3YXJkXCI7XG4gICAgY29uc3QgcmVzdWx0ID0ge1xuICAgICAgLi4ucGFyZW50UmVzdWx0LFxuICAgICAgZmV0Y2hOZXh0UGFnZTogdGhpcy5mZXRjaE5leHRQYWdlLFxuICAgICAgZmV0Y2hQcmV2aW91c1BhZ2U6IHRoaXMuZmV0Y2hQcmV2aW91c1BhZ2UsXG4gICAgICBoYXNOZXh0UGFnZTogaGFzTmV4dFBhZ2Uob3B0aW9ucywgc3RhdGUuZGF0YSksXG4gICAgICBoYXNQcmV2aW91c1BhZ2U6IGhhc1ByZXZpb3VzUGFnZShvcHRpb25zLCBzdGF0ZS5kYXRhKSxcbiAgICAgIGlzRmV0Y2hOZXh0UGFnZUVycm9yLFxuICAgICAgaXNGZXRjaGluZ05leHRQYWdlLFxuICAgICAgaXNGZXRjaFByZXZpb3VzUGFnZUVycm9yLFxuICAgICAgaXNGZXRjaGluZ1ByZXZpb3VzUGFnZSxcbiAgICAgIGlzUmVmZXRjaEVycm9yOiBpc1JlZmV0Y2hFcnJvciAmJiAhaXNGZXRjaE5leHRQYWdlRXJyb3IgJiYgIWlzRmV0Y2hQcmV2aW91c1BhZ2VFcnJvcixcbiAgICAgIGlzUmVmZXRjaGluZzogaXNSZWZldGNoaW5nICYmICFpc0ZldGNoaW5nTmV4dFBhZ2UgJiYgIWlzRmV0Y2hpbmdQcmV2aW91c1BhZ2VcbiAgICB9O1xuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbn07XG5leHBvcnQge1xuICBJbmZpbml0ZVF1ZXJ5T2JzZXJ2ZXJcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmZpbml0ZVF1ZXJ5T2JzZXJ2ZXIuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutation.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n// src/mutation.ts\n\n\n\nvar Mutation = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  #observers;\n  #mutationCache;\n  #retryer;\n  constructor(config) {\n    super();\n    this.mutationId = config.mutationId;\n    this.#mutationCache = config.mutationCache;\n    this.#observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n  setOptions(options) {\n    this.options = options;\n    this.updateGcTime(this.options.gcTime);\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  addObserver(observer) {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer);\n      this.clearGcTimeout();\n      this.#mutationCache.notify({\n        type: \"observerAdded\",\n        mutation: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    this.#observers = this.#observers.filter((x) => x !== observer);\n    this.scheduleGc();\n    this.#mutationCache.notify({\n      type: \"observerRemoved\",\n      mutation: this,\n      observer\n    });\n  }\n  optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === \"pending\") {\n        this.scheduleGc();\n      } else {\n        this.#mutationCache.remove(this);\n      }\n    }\n  }\n  continue() {\n    return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n    this.execute(this.state.variables);\n  }\n  async execute(variables) {\n    this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_1__.createRetryer)({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error(\"No mutationFn found\"));\n        }\n        return this.options.mutationFn(variables);\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue: () => {\n        this.#dispatch({ type: \"continue\" });\n      },\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this)\n    });\n    const restored = this.state.status === \"pending\";\n    const isPaused = !this.#retryer.canStart();\n    try {\n      if (!restored) {\n        this.#dispatch({ type: \"pending\", variables, isPaused });\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this\n        );\n        const context = await this.options.onMutate?.(variables);\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: \"pending\",\n            context,\n            variables,\n            isPaused\n          });\n        }\n      }\n      const data = await this.#retryer.start();\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSuccess?.(data, variables, this.state.context);\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSettled?.(data, null, variables, this.state.context);\n      this.#dispatch({ type: \"success\", data });\n      return data;\n    } catch (error) {\n      try {\n        await this.#mutationCache.config.onError?.(\n          error,\n          variables,\n          this.state.context,\n          this\n        );\n        await this.options.onError?.(\n          error,\n          variables,\n          this.state.context\n        );\n        await this.#mutationCache.config.onSettled?.(\n          void 0,\n          error,\n          this.state.variables,\n          this.state.context,\n          this\n        );\n        await this.options.onSettled?.(\n          void 0,\n          error,\n          variables,\n          this.state.context\n        );\n        throw error;\n      } finally {\n        this.#dispatch({ type: \"error\", error });\n      }\n    } finally {\n      this.#mutationCache.runNext(this);\n    }\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            isPaused: true\n          };\n        case \"continue\":\n          return {\n            ...state,\n            isPaused: false\n          };\n        case \"pending\":\n          return {\n            ...state,\n            context: action.context,\n            data: void 0,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: \"pending\",\n            variables: action.variables,\n            submittedAt: Date.now()\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: \"success\",\n            isPaused: false\n          };\n        case \"error\":\n          return {\n            ...state,\n            data: void 0,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: \"error\"\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action);\n      });\n      this.#mutationCache.notify({\n        mutation: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction getDefaultState() {\n  return {\n    context: void 0,\n    data: void 0,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: \"idle\",\n    variables: void 0,\n    submittedAt: 0\n  };\n}\n\n//# sourceMappingURL=mutation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutationCache.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationCache: () => (/* binding */ MutationCache)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _mutation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/mutationCache.ts\n\n\n\n\nvar MutationCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#mutations = /* @__PURE__ */ new Map();\n    this.#mutationId = Date.now();\n  }\n  #mutations;\n  #mutationId;\n  build(client, options, state) {\n    const mutation = new _mutation_js__WEBPACK_IMPORTED_MODULE_1__.Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state\n    });\n    this.add(mutation);\n    return mutation;\n  }\n  add(mutation) {\n    const scope = scopeFor(mutation);\n    const mutations = this.#mutations.get(scope) ?? [];\n    mutations.push(mutation);\n    this.#mutations.set(scope, mutations);\n    this.notify({ type: \"added\", mutation });\n  }\n  remove(mutation) {\n    const scope = scopeFor(mutation);\n    if (this.#mutations.has(scope)) {\n      const mutations = this.#mutations.get(scope)?.filter((x) => x !== mutation);\n      if (mutations) {\n        if (mutations.length === 0) {\n          this.#mutations.delete(scope);\n        } else {\n          this.#mutations.set(scope, mutations);\n        }\n      }\n    }\n    this.notify({ type: \"removed\", mutation });\n  }\n  canRun(mutation) {\n    const firstPendingMutation = this.#mutations.get(scopeFor(mutation))?.find((m) => m.state.status === \"pending\");\n    return !firstPendingMutation || firstPendingMutation === mutation;\n  }\n  runNext(mutation) {\n    const foundMutation = this.#mutations.get(scopeFor(mutation))?.find((m) => m !== mutation && m.state.isPaused);\n    return foundMutation?.continue() ?? Promise.resolve();\n  }\n  clear() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.getAll().forEach((mutation) => {\n        this.remove(mutation);\n      });\n    });\n  }\n  getAll() {\n    return [...this.#mutations.values()].flat();\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (mutation) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(defaultedFilters, mutation)\n    );\n  }\n  findAll(filters = {}) {\n    return this.getAll().filter((mutation) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation));\n  }\n  notify(event) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  resumePausedMutations() {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused);\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(\n      () => Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(_utils_js__WEBPACK_IMPORTED_MODULE_3__.noop))\n      )\n    );\n  }\n};\nfunction scopeFor(mutation) {\n  return mutation.options.scope?.id ?? String(mutation.mutationId);\n}\n\n//# sourceMappingURL=mutationCache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationObserver.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutationObserver.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationObserver: () => (/* binding */ MutationObserver)\n/* harmony export */ });\n/* harmony import */ var _mutation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mutation.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/mutationObserver.ts\n\n\n\n\nvar MutationObserver = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #client;\n  #currentResult = void 0;\n  #currentMutation;\n  #mutateOptions;\n  constructor(client, options) {\n    super();\n    this.#client = client;\n    this.setOptions(options);\n    this.bindMethods();\n    this.#updateResult();\n  }\n  bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    this.options = this.#client.defaultMutationOptions(options);\n    if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.shallowEqualObjects)(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: \"observerOptionsUpdated\",\n        mutation: this.#currentMutation,\n        observer: this\n      });\n    }\n    if (prevOptions?.mutationKey && this.options.mutationKey && (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashKey)(prevOptions.mutationKey) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashKey)(this.options.mutationKey)) {\n      this.reset();\n    } else if (this.#currentMutation?.state.status === \"pending\") {\n      this.#currentMutation.setOptions(this.options);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this);\n    }\n  }\n  onMutationUpdate(action) {\n    this.#updateResult();\n    this.#notify(action);\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  reset() {\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = void 0;\n    this.#updateResult();\n    this.#notify();\n  }\n  mutate(variables, options) {\n    this.#mutateOptions = options;\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = this.#client.getMutationCache().build(this.#client, this.options);\n    this.#currentMutation.addObserver(this);\n    return this.#currentMutation.execute(variables);\n  }\n  #updateResult() {\n    const state = this.#currentMutation?.state ?? (0,_mutation_js__WEBPACK_IMPORTED_MODULE_2__.getDefaultState)();\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === \"pending\",\n      isSuccess: state.status === \"success\",\n      isError: state.status === \"error\",\n      isIdle: state.status === \"idle\",\n      mutate: this.mutate,\n      reset: this.reset\n    };\n  }\n  #notify(action) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables;\n        const context = this.#currentResult.context;\n        if (action?.type === \"success\") {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context);\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context);\n        } else if (action?.type === \"error\") {\n          this.#mutateOptions.onError?.(action.error, variables, context);\n          this.#mutateOptions.onSettled?.(\n            void 0,\n            action.error,\n            variables,\n            context\n          );\n        }\n      }\n      this.listeners.forEach((listener) => {\n        listener(this.#currentResult);\n      });\n    });\n  }\n};\n\n//# sourceMappingURL=mutationObserver.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/notifyManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNotifyManager: () => (/* binding */ createNotifyManager),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n// src/notifyManager.ts\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n  let notifyFn = (callback) => {\n    callback();\n  };\n  let batchNotifyFn = (callback) => {\n    callback();\n  };\n  let scheduleFn = (cb) => setTimeout(cb, 0);\n  const setScheduler = (fn) => {\n    scheduleFn = fn;\n  };\n  const batch = (callback) => {\n    let result;\n    transactions++;\n    try {\n      result = callback();\n    } finally {\n      transactions--;\n      if (!transactions) {\n        flush();\n      }\n    }\n    return result;\n  };\n  const schedule = (callback) => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  const batchCalls = (callback) => {\n    return (...args) => {\n      schedule(() => {\n        callback(...args);\n      });\n    };\n  };\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  const setNotifyFunction = (fn) => {\n    notifyFn = fn;\n  };\n  const setBatchNotifyFunction = (fn) => {\n    batchNotifyFn = fn;\n  };\n  return {\n    batch,\n    batchCalls,\n    schedule,\n    setNotifyFunction,\n    setBatchNotifyFunction,\n    setScheduler\n  };\n}\nvar notifyManager = createNotifyManager();\n\n//# sourceMappingURL=notifyManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/onlineManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/onlineManager.ts\n\n\nvar OnlineManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #online = true;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onOnline) => {\n      if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true);\n        const offlineListener = () => onOnline(false);\n        window.addEventListener(\"online\", onlineListener, false);\n        window.addEventListener(\"offline\", offlineListener, false);\n        return () => {\n          window.removeEventListener(\"online\", onlineListener);\n          window.removeEventListener(\"offline\", offlineListener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(this.setOnline.bind(this));\n  }\n  setOnline(online) {\n    const changed = this.#online !== online;\n    if (changed) {\n      this.#online = online;\n      this.listeners.forEach((listener) => {\n        listener(online);\n      });\n    }\n  }\n  isOnline() {\n    return this.#online;\n  }\n};\nvar onlineManager = new OnlineManager();\n\n//# sourceMappingURL=onlineManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/query.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query),\n/* harmony export */   fetchState: () => (/* binding */ fetchState)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n// src/query.ts\n\n\n\n\nvar Query = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#cache = config.cache;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = config.state || getDefaultState(this.options);\n    this.state = this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = { ...this.#defaultOptions, ...options };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceData)(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({ type: \"setState\", state, setStateOptions });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({ silent: true });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(\n      (observer) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(observer.options.enabled, this) !== false\n    );\n  }\n  isDisabled() {\n    return this.getObserversCount() > 0 && !this.isActive();\n  }\n  isStale() {\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale\n      );\n    }\n    return this.state.data === void 0;\n  }\n  isStaleByTime(staleTime = 0) {\n    return this.state.isInvalidated || this.state.data === void 0 || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({ type: \"observerAdded\", query: this, observer });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({ type: \"observerRemoved\", query: this, observer });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: \"invalidate\" });\n    }\n  }\n  fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({ silent: true });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (true) {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`\n        );\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = (object) => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureQueryFn)(this.options, fetchOptions);\n      const queryFnContext = {\n        queryKey: this.queryKey,\n        meta: this.meta\n      };\n      addSignalProperty(queryFnContext);\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn,\n          queryFnContext,\n          this\n        );\n      }\n      return queryFn(queryFnContext);\n    };\n    const context = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn\n    };\n    addSignalProperty(context);\n    this.options.behavior?.onFetch(\n      context,\n      this\n    );\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({ type: \"fetch\", meta: context.fetchOptions?.meta });\n    }\n    const onError = (error) => {\n      if (!((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.silent)) {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n      if (!(0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error)) {\n        this.#cache.config.onError?.(\n          error,\n          this\n        );\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this\n        );\n      }\n      if (!this.isFetchingOptimistic) {\n        this.scheduleGc();\n      }\n      this.isFetchingOptimistic = false;\n    };\n    this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.createRetryer)({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === void 0) {\n          if (true) {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`\n            );\n          }\n          onError(new Error(`${this.queryHash} data is undefined`));\n          return;\n        }\n        this.setData(data);\n        this.#cache.config.onSuccess?.(data, this);\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this\n        );\n        if (!this.isFetchingOptimistic) {\n          this.scheduleGc();\n        }\n        this.isFetchingOptimistic = false;\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue: () => {\n        this.#dispatch({ type: \"continue\" });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    return this.#retryer.start();\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            }\n          };\n        case \"error\":\n          const error = action.error;\n          if ((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: \"idle\" };\n          }\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({ query: this, type: \"updated\", action });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.canFetch)(options.networkMode) ? \"fetching\" : \"paused\",\n    ...data === void 0 && {\n      error: null,\n      status: \"pending\"\n    }\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\n\n//# sourceMappingURL=query.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryCache.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryCache: () => (/* binding */ QueryCache)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/queryCache.ts\n\n\n\n\nvar QueryCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#queries = /* @__PURE__ */ new Map();\n  }\n  #queries;\n  build(client, options, state) {\n    const queryKey = options.queryKey;\n    const queryHash = options.queryHash ?? (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);\n    let query = this.get(queryHash);\n    if (!query) {\n      query = new _query_js__WEBPACK_IMPORTED_MODULE_2__.Query({\n        cache: this,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n    return query;\n  }\n  add(query) {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query);\n      this.notify({\n        type: \"added\",\n        query\n      });\n    }\n  }\n  remove(query) {\n    const queryInMap = this.#queries.get(query.queryHash);\n    if (queryInMap) {\n      query.destroy();\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash);\n      }\n      this.notify({ type: \"removed\", query });\n    }\n  }\n  clear() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query);\n      });\n    });\n  }\n  get(queryHash) {\n    return this.#queries.get(queryHash);\n  }\n  getAll() {\n    return [...this.#queries.values()];\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (query) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(defaultedFilters, query)\n    );\n  }\n  findAll(filters = {}) {\n    const queries = this.getAll();\n    return Object.keys(filters).length > 0 ? queries.filter((query) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query)) : queries;\n  }\n  notify(event) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  onFocus() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus();\n      });\n    });\n  }\n  onOnline() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline();\n      });\n    });\n  }\n};\n\n//# sourceMappingURL=queryCache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryClient.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClient: () => (/* binding */ QueryClient)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _queryCache_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./queryCache.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\");\n/* harmony import */ var _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutationCache.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\");\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./infiniteQueryBehavior.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\");\n// src/queryClient.ts\n\n\n\n\n\n\n\nvar QueryClient = class {\n  #queryCache;\n  #mutationCache;\n  #defaultOptions;\n  #queryDefaults;\n  #mutationDefaults;\n  #mountCount;\n  #unsubscribeFocus;\n  #unsubscribeOnline;\n  constructor(config = {}) {\n    this.#queryCache = config.queryCache || new _queryCache_js__WEBPACK_IMPORTED_MODULE_0__.QueryCache();\n    this.#mutationCache = config.mutationCache || new _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__.MutationCache();\n    this.#defaultOptions = config.defaultOptions || {};\n    this.#queryDefaults = /* @__PURE__ */ new Map();\n    this.#mutationDefaults = /* @__PURE__ */ new Map();\n    this.#mountCount = 0;\n  }\n  mount() {\n    this.#mountCount++;\n    if (this.#mountCount !== 1)\n      return;\n    this.#unsubscribeFocus = _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations();\n        this.#queryCache.onFocus();\n      }\n    });\n    this.#unsubscribeOnline = _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations();\n        this.#queryCache.onOnline();\n      }\n    });\n  }\n  unmount() {\n    this.#mountCount--;\n    if (this.#mountCount !== 0)\n      return;\n    this.#unsubscribeFocus?.();\n    this.#unsubscribeFocus = void 0;\n    this.#unsubscribeOnline?.();\n    this.#unsubscribeOnline = void 0;\n  }\n  isFetching(filters) {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: \"fetching\" }).length;\n  }\n  isMutating(filters) {\n    return this.#mutationCache.findAll({ ...filters, status: \"pending\" }).length;\n  }\n  getQueryData(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state.data;\n  }\n  ensureQueryData(options) {\n    const cachedData = this.getQueryData(options.queryKey);\n    if (cachedData === void 0)\n      return this.fetchQuery(options);\n    else {\n      const defaultedOptions = this.defaultQueryOptions(options);\n      const query = this.#queryCache.build(this, defaultedOptions);\n      if (options.revalidateIfStale && query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query))) {\n        void this.prefetchQuery(defaultedOptions);\n      }\n      return Promise.resolve(cachedData);\n    }\n  }\n  getQueriesData(filters) {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n  setQueryData(queryKey, updater, options) {\n    const defaultedOptions = this.defaultQueryOptions({ queryKey });\n    const query = this.#queryCache.get(\n      defaultedOptions.queryHash\n    );\n    const prevData = query?.state.data;\n    const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.functionalUpdate)(updater, prevData);\n    if (data === void 0) {\n      return void 0;\n    }\n    return this.#queryCache.build(this, defaultedOptions).setData(data, { ...options, manual: true });\n  }\n  setQueriesData(filters, updater, options) {\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map(({ queryKey }) => [\n        queryKey,\n        this.setQueryData(queryKey, updater, options)\n      ])\n    );\n  }\n  getQueryState(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state;\n  }\n  removeQueries(filters) {\n    const queryCache = this.#queryCache;\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query);\n      });\n    });\n  }\n  resetQueries(filters, options) {\n    const queryCache = this.#queryCache;\n    const refetchFilters = {\n      type: \"active\",\n      ...filters\n    };\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset();\n      });\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n  cancelQueries(filters = {}, cancelOptions = {}) {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions };\n    const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map((query) => query.cancel(defaultedCancelOptions))\n    );\n    return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  invalidateQueries(filters = {}, options = {}) {\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate();\n      });\n      if (filters.refetchType === \"none\") {\n        return Promise.resolve();\n      }\n      const refetchFilters = {\n        ...filters,\n        type: filters.refetchType ?? filters.type ?? \"active\"\n      };\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n  refetchQueries(filters = {}, options) {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options?.cancelRefetch ?? true\n    };\n    const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).filter((query) => !query.isDisabled()).map((query) => {\n        let promise = query.fetch(void 0, fetchOptions);\n        if (!fetchOptions.throwOnError) {\n          promise = promise.catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n        }\n        return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n      })\n    );\n    return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  fetchQuery(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    if (defaultedOptions.retry === void 0) {\n      defaultedOptions.retry = false;\n    }\n    const query = this.#queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query)\n    ) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n  prefetchQuery(options) {\n    return this.fetchQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  fetchInfiniteQuery(options) {\n    options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n    return this.fetchQuery(options);\n  }\n  prefetchInfiniteQuery(options) {\n    return this.fetchInfiniteQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  resumePausedMutations() {\n    if (_onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations();\n    }\n    return Promise.resolve();\n  }\n  getQueryCache() {\n    return this.#queryCache;\n  }\n  getMutationCache() {\n    return this.#mutationCache;\n  }\n  getDefaultOptions() {\n    return this.#defaultOptions;\n  }\n  setDefaultOptions(options) {\n    this.#defaultOptions = options;\n  }\n  setQueryDefaults(queryKey, options) {\n    this.#queryDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(queryKey), {\n      queryKey,\n      defaultOptions: options\n    });\n  }\n  getQueryDefaults(queryKey) {\n    const defaults = [...this.#queryDefaults.values()];\n    let result = {};\n    defaults.forEach((queryDefault) => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(queryKey, queryDefault.queryKey)) {\n        result = { ...result, ...queryDefault.defaultOptions };\n      }\n    });\n    return result;\n  }\n  setMutationDefaults(mutationKey, options) {\n    this.#mutationDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(mutationKey), {\n      mutationKey,\n      defaultOptions: options\n    });\n  }\n  getMutationDefaults(mutationKey) {\n    const defaults = [...this.#mutationDefaults.values()];\n    let result = {};\n    defaults.forEach((queryDefault) => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(mutationKey, queryDefault.mutationKey)) {\n        result = { ...result, ...queryDefault.defaultOptions };\n      }\n    });\n    return result;\n  }\n  defaultQueryOptions(options) {\n    if (options._defaulted) {\n      return options;\n    }\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashQueryKeyByOptions)(\n        defaultedOptions.queryKey,\n        defaultedOptions\n      );\n    }\n    if (defaultedOptions.refetchOnReconnect === void 0) {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n    }\n    if (defaultedOptions.throwOnError === void 0) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n    }\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = \"offlineFirst\";\n    }\n    if (defaultedOptions.enabled !== true && defaultedOptions.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_4__.skipToken) {\n      defaultedOptions.enabled = false;\n    }\n    return defaultedOptions;\n  }\n  defaultMutationOptions(options) {\n    if (options?._defaulted) {\n      return options;\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n  clear() {\n    this.#queryCache.clear();\n    this.#mutationCache.clear();\n  }\n};\n\n//# sourceMappingURL=queryClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryObserver.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryObserver: () => (/* binding */ QueryObserver)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./query.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\");\n// src/queryObserver.ts\n\n\n\n\n\nvar QueryObserver = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(client, options) {\n    super();\n    this.options = options;\n    this.#client = client;\n    this.#selectError = null;\n    this.bindMethods();\n    this.setOptions(options);\n  }\n  #client;\n  #currentQuery = void 0;\n  #currentQueryInitialState = void 0;\n  #currentResult = void 0;\n  #currentResultState;\n  #currentResultOptions;\n  #selectError;\n  #selectFn;\n  #selectResult;\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData;\n  #staleTimeoutId;\n  #refetchIntervalId;\n  #currentRefetchInterval;\n  #trackedProps = /* @__PURE__ */ new Set();\n  bindMethods() {\n    this.refetch = this.refetch.bind(this);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this);\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch();\n      } else {\n        this.updateResult();\n      }\n      this.#updateTimers();\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnReconnect\n    );\n  }\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus\n    );\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.#clearStaleTimeout();\n    this.#clearRefetchInterval();\n    this.#currentQuery.removeObserver(this);\n  }\n  setOptions(options, notifyOptions) {\n    const prevOptions = this.options;\n    const prevQuery = this.#currentQuery;\n    this.options = this.#client.defaultQueryOptions(options);\n    if (this.options.enabled !== void 0 && typeof this.options.enabled !== \"boolean\" && typeof this.options.enabled !== \"function\" && typeof (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== \"boolean\") {\n      throw new Error(\n        \"Expected enabled to be a boolean or a callback that returns a boolean\"\n      );\n    }\n    this.#updateQuery();\n    this.#currentQuery.setOptions(this.options);\n    if (prevOptions._defaulted && !(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.shallowEqualObjects)(this.options, prevOptions)) {\n      this.#client.getQueryCache().notify({\n        type: \"observerOptionsUpdated\",\n        query: this.#currentQuery,\n        observer: this\n      });\n    }\n    const mounted = this.hasListeners();\n    if (mounted && shouldFetchOptionally(\n      this.#currentQuery,\n      prevQuery,\n      this.options,\n      prevOptions\n    )) {\n      this.#executeFetch();\n    }\n    this.updateResult(notifyOptions);\n    if (mounted && (this.#currentQuery !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(prevOptions.enabled, this.#currentQuery) || (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveStaleTime)(this.options.staleTime, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveStaleTime)(prevOptions.staleTime, this.#currentQuery))) {\n      this.#updateStaleTimeout();\n    }\n    const nextRefetchInterval = this.#computeRefetchInterval();\n    if (mounted && (this.#currentQuery !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(prevOptions.enabled, this.#currentQuery) || nextRefetchInterval !== this.#currentRefetchInterval)) {\n      this.#updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n  getOptimisticResult(options) {\n    const query = this.#client.getQueryCache().build(this.#client, options);\n    const result = this.createResult(query, options);\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      this.#currentResult = result;\n      this.#currentResultOptions = this.options;\n      this.#currentResultState = this.#currentQuery.state;\n    }\n    return result;\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  trackResult(result, onPropTracked) {\n    const trackedResult = {};\n    Object.keys(result).forEach((key) => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackProp(key);\n          onPropTracked?.(key);\n          return result[key];\n        }\n      });\n    });\n    return trackedResult;\n  }\n  trackProp(key) {\n    this.#trackedProps.add(key);\n  }\n  getCurrentQuery() {\n    return this.#currentQuery;\n  }\n  refetch({ ...options } = {}) {\n    return this.fetch({\n      ...options\n    });\n  }\n  fetchOptimistic(options) {\n    const defaultedOptions = this.#client.defaultQueryOptions(options);\n    const query = this.#client.getQueryCache().build(this.#client, defaultedOptions);\n    query.isFetchingOptimistic = true;\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n  fetch(fetchOptions) {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true\n    }).then(() => {\n      this.updateResult();\n      return this.#currentResult;\n    });\n  }\n  #executeFetch(fetchOptions) {\n    this.#updateQuery();\n    let promise = this.#currentQuery.fetch(\n      this.options,\n      fetchOptions\n    );\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop);\n    }\n    return promise;\n  }\n  #updateStaleTimeout() {\n    this.#clearStaleTimeout();\n    const staleTime = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveStaleTime)(\n      this.options.staleTime,\n      this.#currentQuery\n    );\n    if (_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer || this.#currentResult.isStale || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.isValidTimeout)(staleTime)) {\n      return;\n    }\n    const time = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.#currentResult.dataUpdatedAt, staleTime);\n    const timeout = time + 1;\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n  #computeRefetchInterval() {\n    return (typeof this.options.refetchInterval === \"function\" ? this.options.refetchInterval(this.#currentQuery) : this.options.refetchInterval) ?? false;\n  }\n  #updateRefetchInterval(nextInterval) {\n    this.#clearRefetchInterval();\n    this.#currentRefetchInterval = nextInterval;\n    if (_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer || (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(this.options.enabled, this.#currentQuery) === false || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.isValidTimeout)(this.#currentRefetchInterval) || this.#currentRefetchInterval === 0) {\n      return;\n    }\n    this.#refetchIntervalId = setInterval(() => {\n      if (this.options.refetchIntervalInBackground || _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.isFocused()) {\n        this.#executeFetch();\n      }\n    }, this.#currentRefetchInterval);\n  }\n  #updateTimers() {\n    this.#updateStaleTimeout();\n    this.#updateRefetchInterval(this.#computeRefetchInterval());\n  }\n  #clearStaleTimeout() {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId);\n      this.#staleTimeoutId = void 0;\n    }\n  }\n  #clearRefetchInterval() {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId);\n      this.#refetchIntervalId = void 0;\n    }\n  }\n  createResult(query, options) {\n    const prevQuery = this.#currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.#currentResult;\n    const prevResultState = this.#currentResultState;\n    const prevResultOptions = this.#currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.#currentQueryInitialState;\n    const { state } = query;\n    let newState = { ...state };\n    let isPlaceholderData = false;\n    let data;\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...(0,_query_js__WEBPACK_IMPORTED_MODULE_3__.fetchState)(state.data, query.options)\n        };\n      }\n      if (options._optimisticResults === \"isRestoring\") {\n        newState.fetchStatus = \"idle\";\n      }\n    }\n    let { error, errorUpdatedAt, status } = newState;\n    if (options.select && newState.data !== void 0) {\n      if (prevResult && newState.data === prevResultState?.data && options.select === this.#selectFn) {\n        data = this.#selectResult;\n      } else {\n        try {\n          this.#selectFn = options.select;\n          data = options.select(newState.data);\n          data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceData)(prevResult?.data, data, options);\n          this.#selectResult = data;\n          this.#selectError = null;\n        } catch (selectError) {\n          this.#selectError = selectError;\n        }\n      }\n    } else {\n      data = newState.data;\n    }\n    if (options.placeholderData !== void 0 && data === void 0 && status === \"pending\") {\n      let placeholderData;\n      if (prevResult?.isPlaceholderData && options.placeholderData === prevResultOptions?.placeholderData) {\n        placeholderData = prevResult.data;\n      } else {\n        placeholderData = typeof options.placeholderData === \"function\" ? options.placeholderData(\n          this.#lastQueryWithDefinedData?.state.data,\n          this.#lastQueryWithDefinedData\n        ) : options.placeholderData;\n        if (options.select && placeholderData !== void 0) {\n          try {\n            placeholderData = options.select(placeholderData);\n            this.#selectError = null;\n          } catch (selectError) {\n            this.#selectError = selectError;\n          }\n        }\n      }\n      if (placeholderData !== void 0) {\n        status = \"success\";\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceData)(\n          prevResult?.data,\n          placeholderData,\n          options\n        );\n        isPlaceholderData = true;\n      }\n    }\n    if (this.#selectError) {\n      error = this.#selectError;\n      data = this.#selectResult;\n      errorUpdatedAt = Date.now();\n      status = \"error\";\n    }\n    const isFetching = newState.fetchStatus === \"fetching\";\n    const isPending = status === \"pending\";\n    const isError = status === \"error\";\n    const isLoading = isPending && isFetching;\n    const hasData = data !== void 0;\n    const result = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === \"success\",\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount: newState.dataUpdateCount > queryInitialState.dataUpdateCount || newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === \"paused\",\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch\n    };\n    return result;\n  }\n  updateResult(notifyOptions) {\n    const prevResult = this.#currentResult;\n    const nextResult = this.createResult(this.#currentQuery, this.options);\n    this.#currentResultState = this.#currentQuery.state;\n    this.#currentResultOptions = this.options;\n    if (this.#currentResultState.data !== void 0) {\n      this.#lastQueryWithDefinedData = this.#currentQuery;\n    }\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.shallowEqualObjects)(nextResult, prevResult)) {\n      return;\n    }\n    this.#currentResult = nextResult;\n    const defaultNotifyOptions = {};\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n      const { notifyOnChangeProps } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === \"function\" ? notifyOnChangeProps() : notifyOnChangeProps;\n      if (notifyOnChangePropsValue === \"all\" || !notifyOnChangePropsValue && !this.#trackedProps.size) {\n        return true;\n      }\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.#trackedProps\n      );\n      if (this.options.throwOnError) {\n        includedProps.add(\"error\");\n      }\n      return Object.keys(this.#currentResult).some((key) => {\n        const typedKey = key;\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n    if (notifyOptions?.listeners !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true;\n    }\n    this.#notify({ ...defaultNotifyOptions, ...notifyOptions });\n  }\n  #updateQuery() {\n    const query = this.#client.getQueryCache().build(this.#client, this.options);\n    if (query === this.#currentQuery) {\n      return;\n    }\n    const prevQuery = this.#currentQuery;\n    this.#currentQuery = query;\n    this.#currentQueryInitialState = query.state;\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n  onQueryUpdate() {\n    this.updateResult();\n    if (this.hasListeners()) {\n      this.#updateTimers();\n    }\n  }\n  #notify(notifyOptions) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batch(() => {\n      if (notifyOptions.listeners) {\n        this.listeners.forEach((listener) => {\n          listener(this.#currentResult);\n        });\n      }\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: \"observerResultsUpdated\"\n      });\n    });\n  }\n};\nfunction shouldLoadOnMount(query, options) {\n  return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(options.enabled, query) !== false && query.state.data === void 0 && !(query.state.status === \"error\" && options.retryOnMount === false);\n}\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.data !== void 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\nfunction shouldFetchOn(query, options, field) {\n  if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(options.enabled, query) !== false) {\n    const value = typeof field === \"function\" ? field(query) : field;\n    return value === \"always\" || value !== false && isStale(query, options);\n  }\n  return false;\n}\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return (query !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(prevOptions.enabled, query) === false) && (!options.suspense || query.state.status !== \"error\") && isStale(query, options);\n}\nfunction isStale(query, options) {\n  return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(options.enabled, query) !== false && query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveStaleTime)(options.staleTime, query));\n}\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult) {\n  if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.shallowEqualObjects)(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  }\n  return false;\n}\n\n//# sourceMappingURL=queryObserver.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/removable.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Removable: () => (/* binding */ Removable)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/removable.ts\n\nvar Removable = class {\n  #gcTimeout;\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isValidTimeout)(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.gcTime);\n    }\n  }\n  updateGcTime(newGcTime) {\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (_utils_js__WEBPACK_IMPORTED_MODULE_0__.isServer ? Infinity : 5 * 60 * 1e3)\n    );\n  }\n  clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout);\n      this.#gcTimeout = void 0;\n    }\n  }\n};\n\n//# sourceMappingURL=removable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3JlbW92YWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ3NEO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSx5REFBYztBQUN0QjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsK0NBQVE7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3RoZS1jYW52YXMvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3JlbW92YWJsZS5qcz84OGY5Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9yZW1vdmFibGUudHNcbmltcG9ydCB7IGlzU2VydmVyLCBpc1ZhbGlkVGltZW91dCB9IGZyb20gXCIuL3V0aWxzLmpzXCI7XG52YXIgUmVtb3ZhYmxlID0gY2xhc3Mge1xuICAjZ2NUaW1lb3V0O1xuICBkZXN0cm95KCkge1xuICAgIHRoaXMuY2xlYXJHY1RpbWVvdXQoKTtcbiAgfVxuICBzY2hlZHVsZUdjKCkge1xuICAgIHRoaXMuY2xlYXJHY1RpbWVvdXQoKTtcbiAgICBpZiAoaXNWYWxpZFRpbWVvdXQodGhpcy5nY1RpbWUpKSB7XG4gICAgICB0aGlzLiNnY1RpbWVvdXQgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgdGhpcy5vcHRpb25hbFJlbW92ZSgpO1xuICAgICAgfSwgdGhpcy5nY1RpbWUpO1xuICAgIH1cbiAgfVxuICB1cGRhdGVHY1RpbWUobmV3R2NUaW1lKSB7XG4gICAgdGhpcy5nY1RpbWUgPSBNYXRoLm1heChcbiAgICAgIHRoaXMuZ2NUaW1lIHx8IDAsXG4gICAgICBuZXdHY1RpbWUgPz8gKGlzU2VydmVyID8gSW5maW5pdHkgOiA1ICogNjAgKiAxZTMpXG4gICAgKTtcbiAgfVxuICBjbGVhckdjVGltZW91dCgpIHtcbiAgICBpZiAodGhpcy4jZ2NUaW1lb3V0KSB7XG4gICAgICBjbGVhclRpbWVvdXQodGhpcy4jZ2NUaW1lb3V0KTtcbiAgICAgIHRoaXMuI2djVGltZW91dCA9IHZvaWQgMDtcbiAgICB9XG4gIH1cbn07XG5leHBvcnQge1xuICBSZW1vdmFibGVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZW1vdmFibGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/retryer.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   canFetch: () => (/* binding */ canFetch),\n/* harmony export */   createRetryer: () => (/* binding */ createRetryer),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/retryer.ts\n\n\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n  return (networkMode ?? \"online\") === \"online\" ? _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline() : true;\n}\nvar CancelledError = class {\n  constructor(options) {\n    this.revert = options?.revert;\n    this.silent = options?.silent;\n  }\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  let promiseResolve;\n  let promiseReject;\n  const promise = new Promise((outerResolve, outerReject) => {\n    promiseResolve = outerResolve;\n    promiseReject = outerReject;\n  });\n  const cancel = (cancelOptions) => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort?.();\n    }\n  };\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n  const canContinue = () => _focusManager_js__WEBPACK_IMPORTED_MODULE_1__.focusManager.isFocused() && (config.networkMode === \"always\" || _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline()) && config.canRun();\n  const canStart = () => canFetch(config.networkMode) && config.canRun();\n  const resolve = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess?.(value);\n      continueFn?.();\n      promiseResolve(value);\n    }\n  };\n  const reject = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError?.(value);\n      continueFn?.();\n      promiseReject(value);\n    }\n  };\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved || canContinue()) {\n          continueResolve(value);\n        }\n      };\n      config.onPause?.();\n    }).then(() => {\n      continueFn = void 0;\n      if (!isResolved) {\n        config.onContinue?.();\n      }\n    });\n  };\n  const run = () => {\n    if (isResolved) {\n      return;\n    }\n    let promiseOrValue;\n    const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n    try {\n      promiseOrValue = initialPromise ?? config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n    Promise.resolve(promiseOrValue).then(resolve).catch((error) => {\n      if (isResolved) {\n        return;\n      }\n      const retry = config.retry ?? (_utils_js__WEBPACK_IMPORTED_MODULE_2__.isServer ? 0 : 3);\n      const retryDelay = config.retryDelay ?? defaultRetryDelay;\n      const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n      if (isRetryCancelled || !shouldRetry) {\n        reject(error);\n        return;\n      }\n      failureCount++;\n      config.onFail?.(failureCount, error);\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.sleep)(delay).then(() => {\n        return canContinue() ? void 0 : pause();\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  };\n  return {\n    promise,\n    cancel,\n    continue: () => {\n      continueFn?.();\n      return promise;\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      if (canStart()) {\n        run();\n      } else {\n        pause().then(run);\n      }\n      return promise;\n    }\n  };\n}\n\n//# sourceMappingURL=retryer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/subscribable.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\n// src/subscribable.ts\nvar Subscribable = class {\n  constructor() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    this.listeners.add(listener);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(listener);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {\n  }\n  onUnsubscribe() {\n  }\n};\n\n//# sourceMappingURL=subscribable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3N1YnNjcmliYWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcXVlcnktY29yZS9idWlsZC9tb2Rlcm4vc3Vic2NyaWJhYmxlLmpzPzViNWYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3N1YnNjcmliYWJsZS50c1xudmFyIFN1YnNjcmliYWJsZSA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5saXN0ZW5lcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpO1xuICAgIHRoaXMuc3Vic2NyaWJlID0gdGhpcy5zdWJzY3JpYmUuYmluZCh0aGlzKTtcbiAgfVxuICBzdWJzY3JpYmUobGlzdGVuZXIpIHtcbiAgICB0aGlzLmxpc3RlbmVycy5hZGQobGlzdGVuZXIpO1xuICAgIHRoaXMub25TdWJzY3JpYmUoKTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgdGhpcy5saXN0ZW5lcnMuZGVsZXRlKGxpc3RlbmVyKTtcbiAgICAgIHRoaXMub25VbnN1YnNjcmliZSgpO1xuICAgIH07XG4gIH1cbiAgaGFzTGlzdGVuZXJzKCkge1xuICAgIHJldHVybiB0aGlzLmxpc3RlbmVycy5zaXplID4gMDtcbiAgfVxuICBvblN1YnNjcmliZSgpIHtcbiAgfVxuICBvblVuc3Vic2NyaWJlKCkge1xuICB9XG59O1xuZXhwb3J0IHtcbiAgU3Vic2NyaWJhYmxlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3Vic2NyaWJhYmxlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToEnd: () => (/* binding */ addToEnd),\n/* harmony export */   addToStart: () => (/* binding */ addToStart),\n/* harmony export */   ensureQueryFn: () => (/* binding */ ensureQueryFn),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   hashKey: () => (/* binding */ hashKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isPlainArray: () => (/* binding */ isPlainArray),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   keepPreviousData: () => (/* binding */ keepPreviousData),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceData: () => (/* binding */ replaceData),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   resolveEnabled: () => (/* binding */ resolveEnabled),\n/* harmony export */   resolveStaleTime: () => (/* binding */ resolveStaleTime),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   skipToken: () => (/* binding */ skipToken),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n// src/utils.ts\nvar isServer = typeof window === \"undefined\" || \"Deno\" in globalThis;\nfunction noop() {\n  return void 0;\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n  return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n  return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = \"all\",\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n  if (type !== \"all\") {\n    const isActive = query.isActive();\n    if (type === \"active\" && !isActive) {\n      return false;\n    }\n    if (type === \"inactive\" && isActive) {\n      return false;\n    }\n  }\n  if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n    return false;\n  }\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const { exact, status, predicate, mutationKey } = filters;\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n  if (status && mutation.state.status !== status) {\n    return false;\n  }\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = options?.queryKeyHashFn || hashKey;\n  return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n  return JSON.stringify(\n    queryKey,\n    (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n      result[key] = val[key];\n      return result;\n    }, {}) : val\n  );\n}\nfunction partialMatchKey(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    return !Object.keys(b).some((key) => !partialMatchKey(a[key], b[key]));\n  }\n  return false;\n}\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n  const array = isPlainArray(a) && isPlainArray(b);\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aItems = array ? a : Object.keys(a);\n    const aSize = aItems.length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    let equalItems = 0;\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      if ((!array && aItems.includes(key) || array) && a[key] === void 0 && b[key] === void 0) {\n        copy[key] = void 0;\n        equalItems++;\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key]);\n        if (copy[key] === a[key] && a[key] !== void 0) {\n          equalItems++;\n        }\n      }\n    }\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n  return b;\n}\nfunction shallowEqualObjects(a, b) {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (ctor === void 0) {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(ms) {\n  return new Promise((resolve) => {\n    setTimeout(resolve, ms);\n  });\n}\nfunction replaceData(prevData, data, options) {\n  if (typeof options.structuralSharing === \"function\") {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    return replaceEqualDeep(prevData, data);\n  }\n  return data;\n}\nfunction keepPreviousData(previousData) {\n  return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n  const newItems = [...items, item];\n  return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n  const newItems = [item, ...items];\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nvar ensureQueryFn = (options, fetchOptions) => {\n  if (true) {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`\n      );\n    }\n  }\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise;\n  }\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () => Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n  }\n  return options.queryFn;\n};\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientContext: () => (/* binding */ QueryClientContext),\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientContext,QueryClientProvider,useQueryClient auto */ // src/QueryClientProvider.tsx\n\n\nvar QueryClientContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar useQueryClient = (queryClient)=>{\n    const client = react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientContext);\n    if (queryClient) {\n        return queryClient;\n    }\n    if (!client) {\n        throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n    }\n    return client;\n};\nvar QueryClientProvider = ({ client, children })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        client.mount();\n        return ()=>{\n            client.unmount();\n        };\n    }, [\n        client\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryClientContext.Provider, {\n        value: client,\n        children\n    });\n};\n //# sourceMappingURL=QueryClientProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryErrorResetBoundary: () => (/* binding */ QueryErrorResetBoundary),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* binding */ useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryErrorResetBoundary,useQueryErrorResetBoundary auto */ // src/QueryErrorResetBoundary.tsx\n\n\nfunction createValue() {\n    let isReset = false;\n    return {\n        clearReset: ()=>{\n            isReset = false;\n        },\n        reset: ()=>{\n            isReset = true;\n        },\n        isReset: ()=>{\n            return isReset;\n        }\n    };\n}\nvar QueryErrorResetBoundaryContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(createValue());\nvar useQueryErrorResetBoundary = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryErrorResetBoundaryContext);\nvar QueryErrorResetBoundary = ({ children })=>{\n    const [value] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>createValue());\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryErrorResetBoundaryContext.Provider, {\n        value,\n        children: typeof children === \"function\" ? children(value) : children\n    });\n};\n //# sourceMappingURL=QueryErrorResetBoundary.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensurePreventErrorBoundaryRetry: () => (/* binding */ ensurePreventErrorBoundaryRetry),\n/* harmony export */   getHasError: () => (/* binding */ getHasError),\n/* harmony export */   useClearResetErrorBoundary: () => (/* binding */ useClearResetErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/utils.js\");\n/* __next_internal_client_entry_do_not_use__ ensurePreventErrorBoundaryRetry,getHasError,useClearResetErrorBoundary auto */ // src/errorBoundaryUtils.ts\n\n\nvar ensurePreventErrorBoundaryRetry = (options, errorResetBoundary)=>{\n    if (options.suspense || options.throwOnError) {\n        if (!errorResetBoundary.isReset()) {\n            options.retryOnMount = false;\n        }\n    }\n};\nvar useClearResetErrorBoundary = (errorResetBoundary)=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        errorResetBoundary.clearReset();\n    }, [\n        errorResetBoundary\n    ]);\n};\nvar getHasError = ({ result, errorResetBoundary, throwOnError, query })=>{\n    return result.isError && !errorResetBoundary.isReset() && !result.isFetching && query && (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.shouldThrowError)(throwOnError, [\n        result.error,\n        query\n    ]);\n};\n //# sourceMappingURL=errorBoundaryUtils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/isRestoring.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/isRestoring.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IsRestoringProvider: () => (/* binding */ IsRestoringProvider),\n/* harmony export */   useIsRestoring: () => (/* binding */ useIsRestoring)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ IsRestoringProvider,useIsRestoring auto */ // src/isRestoring.ts\n\nvar IsRestoringContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(false);\nvar useIsRestoring = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(IsRestoringContext);\nvar IsRestoringProvider = IsRestoringContext.Provider;\n //# sourceMappingURL=isRestoring.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi9pc1Jlc3RvcmluZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQ3VCO0FBRXZCLElBQU1DLG1DQUEyQkQsZ0RBQUEsQ0FBYztBQUV4QyxJQUFNRyxpQkFBaUIsSUFBWUgsNkNBQUEsQ0FBV0M7QUFDOUMsSUFBTUksc0JBQXNCSixtQkFBbUJLLFFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4uLy4uL3NyYy9pc1Jlc3RvcmluZy50cz82MmE0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmNvbnN0IElzUmVzdG9yaW5nQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQoZmFsc2UpXG5cbmV4cG9ydCBjb25zdCB1c2VJc1Jlc3RvcmluZyA9ICgpID0+IFJlYWN0LnVzZUNvbnRleHQoSXNSZXN0b3JpbmdDb250ZXh0KVxuZXhwb3J0IGNvbnN0IElzUmVzdG9yaW5nUHJvdmlkZXIgPSBJc1Jlc3RvcmluZ0NvbnRleHQuUHJvdmlkZXJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIklzUmVzdG9yaW5nQ29udGV4dCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VJc1Jlc3RvcmluZyIsInVzZUNvbnRleHQiLCJJc1Jlc3RvcmluZ1Byb3ZpZGVyIiwiUHJvdmlkZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/isRestoring.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/suspense.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultThrowOnError: () => (/* binding */ defaultThrowOnError),\n/* harmony export */   ensureStaleTime: () => (/* binding */ ensureStaleTime),\n/* harmony export */   fetchOptimistic: () => (/* binding */ fetchOptimistic),\n/* harmony export */   shouldSuspend: () => (/* binding */ shouldSuspend),\n/* harmony export */   willFetch: () => (/* binding */ willFetch)\n/* harmony export */ });\n// src/suspense.ts\nvar defaultThrowOnError = (_error, query) => query.state.data === void 0;\nvar ensureStaleTime = (defaultedOptions) => {\n  if (defaultedOptions.suspense) {\n    if (typeof defaultedOptions.staleTime !== \"number\") {\n      defaultedOptions.staleTime = 1e3;\n    }\n  }\n};\nvar willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;\nvar shouldSuspend = (defaultedOptions, result) => defaultedOptions?.suspense && result.isPending;\nvar fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).catch(() => {\n  errorResetBoundary.clearReset();\n});\n\n//# sourceMappingURL=suspense.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi9zdXNwZW5zZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQU9DO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9yZWFjdC1xdWVyeS9idWlsZC9tb2Rlcm4vc3VzcGVuc2UuanM/NWUzNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvc3VzcGVuc2UudHNcbnZhciBkZWZhdWx0VGhyb3dPbkVycm9yID0gKF9lcnJvciwgcXVlcnkpID0+IHF1ZXJ5LnN0YXRlLmRhdGEgPT09IHZvaWQgMDtcbnZhciBlbnN1cmVTdGFsZVRpbWUgPSAoZGVmYXVsdGVkT3B0aW9ucykgPT4ge1xuICBpZiAoZGVmYXVsdGVkT3B0aW9ucy5zdXNwZW5zZSkge1xuICAgIGlmICh0eXBlb2YgZGVmYXVsdGVkT3B0aW9ucy5zdGFsZVRpbWUgIT09IFwibnVtYmVyXCIpIHtcbiAgICAgIGRlZmF1bHRlZE9wdGlvbnMuc3RhbGVUaW1lID0gMWUzO1xuICAgIH1cbiAgfVxufTtcbnZhciB3aWxsRmV0Y2ggPSAocmVzdWx0LCBpc1Jlc3RvcmluZykgPT4gcmVzdWx0LmlzTG9hZGluZyAmJiByZXN1bHQuaXNGZXRjaGluZyAmJiAhaXNSZXN0b3Jpbmc7XG52YXIgc2hvdWxkU3VzcGVuZCA9IChkZWZhdWx0ZWRPcHRpb25zLCByZXN1bHQpID0+IGRlZmF1bHRlZE9wdGlvbnM/LnN1c3BlbnNlICYmIHJlc3VsdC5pc1BlbmRpbmc7XG52YXIgZmV0Y2hPcHRpbWlzdGljID0gKGRlZmF1bHRlZE9wdGlvbnMsIG9ic2VydmVyLCBlcnJvclJlc2V0Qm91bmRhcnkpID0+IG9ic2VydmVyLmZldGNoT3B0aW1pc3RpYyhkZWZhdWx0ZWRPcHRpb25zKS5jYXRjaCgoKSA9PiB7XG4gIGVycm9yUmVzZXRCb3VuZGFyeS5jbGVhclJlc2V0KCk7XG59KTtcbmV4cG9ydCB7XG4gIGRlZmF1bHRUaHJvd09uRXJyb3IsXG4gIGVuc3VyZVN0YWxlVGltZSxcbiAgZmV0Y2hPcHRpbWlzdGljLFxuICBzaG91bGRTdXNwZW5kLFxuICB3aWxsRmV0Y2hcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zdXNwZW5zZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBaseQuery: () => (/* binding */ useBaseQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryErrorResetBoundary.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _isRestoring_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isRestoring.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/isRestoring.js\");\n/* harmony import */ var _errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./errorBoundaryUtils.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\");\n/* harmony import */ var _suspense_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./suspense.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js\");\n/* __next_internal_client_entry_do_not_use__ useBaseQuery auto */ // src/useBaseQuery.ts\n\n\n\n\n\n\n\nfunction useBaseQuery(options, Observer, queryClient) {\n    if (true) {\n        if (typeof options !== \"object\" || Array.isArray(options)) {\n            throw new Error('Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object');\n        }\n    }\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)(queryClient);\n    const isRestoring = (0,_isRestoring_js__WEBPACK_IMPORTED_MODULE_2__.useIsRestoring)();\n    const errorResetBoundary = (0,_QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_3__.useQueryErrorResetBoundary)();\n    const defaultedOptions = client.defaultQueryOptions(options);\n    client.getDefaultOptions().queries?._experimental_beforeQuery?.(defaultedOptions);\n    defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n    (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.ensureStaleTime)(defaultedOptions);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.ensurePreventErrorBoundaryRetry)(defaultedOptions, errorResetBoundary);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.useClearResetErrorBoundary)(errorResetBoundary);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>new Observer(client, defaultedOptions));\n    const result = observer.getOptimisticResult(defaultedOptions);\n    react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(react__WEBPACK_IMPORTED_MODULE_0__.useCallback((onStoreChange)=>{\n        const unsubscribe = isRestoring ? ()=>void 0 : observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batchCalls(onStoreChange));\n        observer.updateResult();\n        return unsubscribe;\n    }, [\n        observer,\n        isRestoring\n    ]), ()=>observer.getCurrentResult(), ()=>observer.getCurrentResult());\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        observer.setOptions(defaultedOptions, {\n            listeners: false\n        });\n    }, [\n        defaultedOptions,\n        observer\n    ]);\n    if ((0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.shouldSuspend)(defaultedOptions, result)) {\n        throw (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary);\n    }\n    if ((0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.getHasError)({\n        result,\n        errorResetBoundary,\n        throwOnError: defaultedOptions.throwOnError,\n        query: client.getQueryCache().get(defaultedOptions.queryHash)\n    })) {\n        throw result.error;\n    }\n    ;\n    client.getDefaultOptions().queries?._experimental_afterQuery?.(defaultedOptions, result);\n    return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\n //# sourceMappingURL=useBaseQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInfiniteQuery: () => (/* binding */ useInfiniteQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js\");\n/* harmony import */ var _useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useBaseQuery.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\");\n/* __next_internal_client_entry_do_not_use__ useInfiniteQuery auto */ // src/useInfiniteQuery.ts\n\n\nfunction useInfiniteQuery(options, queryClient) {\n    return (0,_useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__.useBaseQuery)(options, _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.InfiniteQueryObserver, queryClient);\n}\n //# sourceMappingURL=useInfiniteQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useMutation.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMutation: () => (/* binding */ useMutation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationObserver.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/utils.js\");\n/* __next_internal_client_entry_do_not_use__ useMutation auto */ // src/useMutation.ts\n\n\n\n\nfunction useMutation(options, queryClient) {\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)(queryClient);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>new _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__.MutationObserver(client, options));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        observer.setOptions(options);\n    }, [\n        observer,\n        options\n    ]);\n    const result = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(react__WEBPACK_IMPORTED_MODULE_0__.useCallback((onStoreChange)=>observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(onStoreChange)), [\n        observer\n    ]), ()=>observer.getCurrentResult(), ()=>observer.getCurrentResult());\n    const mutate = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((variables, mutateOptions)=>{\n        observer.mutate(variables, mutateOptions).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }, [\n        observer\n    ]);\n    if (result.error && (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.shouldThrowError)(observer.options.throwOnError, [\n        result.error\n    ])) {\n        throw result.error;\n    }\n    return {\n        ...result,\n        mutate,\n        mutateAsync: result.mutate\n    };\n}\n //# sourceMappingURL=useMutation.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/utils.js":
/*!******************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/utils.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError)\n/* harmony export */ });\n// src/utils.ts\nfunction shouldThrowError(throwError, params) {\n  if (typeof throwError === \"function\") {\n    return throwError(...params);\n  }\n  return !!throwError;\n}\nfunction noop() {\n}\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9yZWFjdC1xdWVyeS9idWlsZC9tb2Rlcm4vdXRpbHMuanM/ODQyMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXRpbHMudHNcbmZ1bmN0aW9uIHNob3VsZFRocm93RXJyb3IodGhyb3dFcnJvciwgcGFyYW1zKSB7XG4gIGlmICh0eXBlb2YgdGhyb3dFcnJvciA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgcmV0dXJuIHRocm93RXJyb3IoLi4ucGFyYW1zKTtcbiAgfVxuICByZXR1cm4gISF0aHJvd0Vycm9yO1xufVxuZnVuY3Rpb24gbm9vcCgpIHtcbn1cbmV4cG9ydCB7XG4gIG5vb3AsXG4gIHNob3VsZFRocm93RXJyb3Jcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/utils.js\n");

/***/ })

};
;