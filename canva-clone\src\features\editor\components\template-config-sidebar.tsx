"use client";

import { useState, useEffect } from "react";
import { 
  Settings, 
  Type, 
  Image, 
  Plus, 
  Trash2, 
  Eye, 
  EyeOff,
  Save,
  Globe
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

import { ActiveTool, Editor } from "@/features/editor/types";
import { EditableLayer, EditableFabricObject } from "@/types/template";
import { useUpdateTemplateConfig } from "@/features/projects/api/use-update-template-config";

interface TemplateConfigSidebarProps {
  editor: Editor | undefined;
  activeTool: ActiveTool;
  onChangeActiveTool: (tool: ActiveTool) => void;
  projectId?: string;
  initialData?: {
    isCustomizable?: boolean;
    editableLayers?: string | null;
  };
}

export const TemplateConfigSidebar = ({
  editor,
  activeTool,
  onChangeActiveTool,
  projectId,
  initialData
}: TemplateConfigSidebarProps) => {
  const [editableLayers, setEditableLayers] = useState<EditableLayer[]>([]);
  const [isCustomizable, setIsCustomizable] = useState(false);
  const updateTemplateConfig = useUpdateTemplateConfig(projectId || "");

  const onClose = () => {
    onChangeActiveTool("select");
  };

  const canvas = editor?.canvas;
  const selectedObjects = editor?.selectedObjects || [];

  // Load existing template configuration from database
  useEffect(() => {
    if (initialData) {
      setIsCustomizable(initialData.isCustomizable || false);

      if (initialData.editableLayers) {
        try {
          const parsedLayers: EditableLayer[] = JSON.parse(initialData.editableLayers);
          setEditableLayers(parsedLayers);

          // Apply editable properties to canvas objects
          if (canvas) {
            parsedLayers.forEach((layer) => {
              const canvasObject = canvas.getObjects().find(obj => obj.id === layer.id) as EditableFabricObject;
              if (canvasObject) {
                canvasObject.isEditable = true;
                canvasObject.editableType = layer.type;
                canvasObject.editableName = layer.name;
                canvasObject.editablePlaceholder = layer.placeholder;
                canvasObject.editableConstraints = layer.constraints;
              }
            });
            canvas.renderAll();
          }
        } catch (error) {
          console.error('Failed to parse editable layers:', error);
        }
      }
    }
  }, [initialData, canvas]);

  // Load existing editable layers from canvas objects (fallback)
  useEffect(() => {
    if (!canvas || editableLayers.length > 0) return;

    const layers: EditableLayer[] = [];
    canvas.getObjects().forEach((obj) => {
      const editableObj = obj as EditableFabricObject;
      if (editableObj.isEditable) {
        layers.push({
          id: editableObj.id || '',
          type: editableObj.editableType || 'text',
          name: editableObj.editableName || 'Unnamed Layer',
          originalValue: editableObj.type === 'textbox' ? (editableObj as fabric.Textbox).text : '',
          placeholder: editableObj.editablePlaceholder,
          constraints: editableObj.editableConstraints,
        });
      }
    });

    if (layers.length > 0) {
      setEditableLayers(layers);
      setIsCustomizable(true);
    }
  }, [canvas, editableLayers.length]);

  const makeLayerEditable = (type: 'text' | 'image') => {
    if (!canvas || selectedObjects.length === 0) return;

    const selectedObject = selectedObjects[0] as EditableFabricObject;
    
    // Validate object type
    if (type === 'text' && selectedObject.type !== 'textbox') {
      alert('Please select a text object to make it editable');
      return;
    }
    
    if (type === 'image' && !['image', 'rect', 'circle'].includes(selectedObject.type || '')) {
      alert('Please select an image or shape to make it editable');
      return;
    }

    // Mark object as editable
    selectedObject.isEditable = true;
    selectedObject.editableType = type;
    selectedObject.editableName = `${type === 'text' ? 'Text' : 'Image'} ${editableLayers.length + 1}`;
    
    if (type === 'text') {
      selectedObject.editablePlaceholder = 'Enter your text here...';
    }

    // Add to editable layers list
    const newLayer: EditableLayer = {
      id: selectedObject.id || `layer_${Date.now()}`,
      type,
      name: selectedObject.editableName,
      originalValue: type === 'text' ? (selectedObject as fabric.Textbox).text : '',
      placeholder: selectedObject.editablePlaceholder,
      constraints: {
        maxLength: type === 'text' ? 100 : undefined,
        allowedFormats: type === 'image' ? ['jpg', 'jpeg', 'png', 'gif'] : undefined,
        maxFileSize: type === 'image' ? 5 * 1024 * 1024 : undefined, // 5MB
      },
    };

    // Assign ID if not exists
    if (!selectedObject.id) {
      selectedObject.id = newLayer.id;
    }

    setEditableLayers([...editableLayers, newLayer]);
    // Don't automatically enable the toggle - let user control it manually
    canvas.renderAll();
  };

  const removeEditableLayer = (layerId: string) => {
    // Find and update the canvas object
    const canvasObject = canvas?.getObjects().find(obj => obj.id === layerId) as EditableFabricObject;
    if (canvasObject) {
      canvasObject.isEditable = false;
      delete canvasObject.editableType;
      delete canvasObject.editableName;
      delete canvasObject.editablePlaceholder;
      delete canvasObject.editableConstraints;
    }

    // Remove from layers list
    const updatedLayers = editableLayers.filter(layer => layer.id !== layerId);
    setEditableLayers(updatedLayers);
    // Don't automatically disable the toggle - let user control it manually
    canvas?.renderAll();
  };

  const updateLayerName = (layerId: string, name: string) => {
    const updatedLayers = editableLayers.map(layer =>
      layer.id === layerId ? { ...layer, name } : layer
    );
    setEditableLayers(updatedLayers);

    // Update canvas object
    const canvasObject = canvas?.getObjects().find(obj => obj.id === layerId) as EditableFabricObject;
    if (canvasObject) {
      canvasObject.editableName = name;
    }
  };

  const updateLayerPlaceholder = (layerId: string, placeholder: string) => {
    const updatedLayers = editableLayers.map(layer =>
      layer.id === layerId ? { ...layer, placeholder } : layer
    );
    setEditableLayers(updatedLayers);

    // Update canvas object
    const canvasObject = canvas?.getObjects().find(obj => obj.id === layerId) as EditableFabricObject;
    if (canvasObject) {
      canvasObject.editablePlaceholder = placeholder;
    }
  };

  const saveTemplateConfig = () => {
    if (!projectId) {
      alert('Project ID is required to save template configuration');
      return;
    }

    updateTemplateConfig.mutate({
      isCustomizable,
      editableLayers: JSON.stringify(editableLayers),
    });

    onClose();
  };

  return (
    <div
      className={cn(
        "bg-white relative border-r z-[40] w-[360px] h-full flex flex-col",
        activeTool === "template-config" ? "visible" : "hidden",
      )}
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-purple-600" />
            <h2 className="font-semibold text-gray-900">Template Settings</h2>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            ×
          </Button>
        </div>
        <p className="text-sm text-gray-600 mt-1">
          Configure which elements users can customize
        </p>
      </div>

      <ScrollArea className="flex-1 p-4">
        {/* Template Status */}
        <Card className="mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center space-x-2">
              <Globe className="h-4 w-4" />
              <span>Template Status</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <Label htmlFor="customizable" className="text-sm">
                Make Customizable
              </Label>
              <Switch
                id="customizable"
                checked={isCustomizable}
                onCheckedChange={setIsCustomizable}
              />
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Allow public users to customize this template
            </p>
          </CardContent>
        </Card>

        {/* Add Editable Elements */}
        <Card className="mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Add Editable Elements</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              onClick={() => makeLayerEditable('text')}
              disabled={selectedObjects.length === 0}
            >
              <Type className="h-4 w-4 mr-2" />
              Make Text Editable
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              onClick={() => makeLayerEditable('image')}
              disabled={selectedObjects.length === 0}
            >
              <Image className="h-4 w-4 mr-2" />
              Make Image Replaceable
            </Button>
            {selectedObjects.length === 0 && (
              <p className="text-xs text-gray-500">
                Select an element on the canvas first
              </p>
            )}
          </CardContent>
        </Card>

        {/* Editable Layers List */}
        {editableLayers.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">
                Editable Elements ({editableLayers.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {editableLayers.map((layer) => (
                <div key={layer.id} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant={layer.type === 'text' ? 'default' : 'secondary'}>
                      {layer.type === 'text' ? (
                        <Type className="h-3 w-3 mr-1" />
                      ) : (
                        <Image className="h-3 w-3 mr-1" />
                      )}
                      {layer.type}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeEditableLayer(layer.id)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                  
                  <div className="space-y-2">
                    <div>
                      <Label className="text-xs">Display Name</Label>
                      <Input
                        value={layer.name}
                        onChange={(e) => updateLayerName(layer.id, e.target.value)}
                        className="h-8 text-sm"
                        placeholder="e.g., Your Name, Profile Photo"
                      />
                    </div>
                    
                    {layer.type === 'text' && (
                      <div>
                        <Label className="text-xs">Placeholder Text</Label>
                        <Input
                          value={layer.placeholder || ''}
                          onChange={(e) => updateLayerPlaceholder(layer.id, e.target.value)}
                          className="h-8 text-sm"
                          placeholder="Enter placeholder text..."
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        )}
      </ScrollArea>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <Button
          onClick={saveTemplateConfig}
          className="w-full"
          disabled={updateTemplateConfig.isPending}
        >
          <Save className="h-4 w-4 mr-2" />
          {updateTemplateConfig.isPending ? 'Saving...' : 'Save Template Config'}
        </Button>
      </div>
    </div>
  );
};
